// Popup Script for WhatsApp Business Connector
document.addEventListener('DOMContentLoaded', async () => {
    const popup = new WhatsAppPopup();
    await popup.init();
});

class WhatsAppPopup {
    constructor() {
        this.currentTab = null;
        this.accountStatus = null;
        this.isWhatsAppTab = false;
        
        // Get DOM elements
        this.elements = {
            loading: document.getElementById('loading'),
            mainContent: document.getElementById('main-content'),
            statusDot: document.getElementById('status-dot'),
            statusText: document.getElementById('status-text'),
            accountInfo: document.getElementById('account-info'),
            errorMessage: document.getElementById('error-message'),
            successMessage: document.getElementById('success-message'),
            connectBtn: document.getElementById('connect-btn'),
            dashboardBtn: document.getElementById('dashboard-btn'),
            refreshBtn: document.getElementById('refresh-btn'),
            contactInput: document.getElementById('contact-input'),
            messageInput: document.getElementById('message-input'),
            sendBtn: document.getElementById('send-btn'),
            messagesCount: document.getElementById('messages-count'),
            conversationsCount: document.getElementById('conversations-count'),
            settingsLink: document.getElementById('settings-link')
        };
    }

    async init() {
        try {
            // Get current tab
            this.currentTab = await this.getCurrentTab();
            this.isWhatsAppTab = this.currentTab?.url?.includes('web.whatsapp.com') || false;
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Load initial status
            await this.loadStatus();
            
            // Show main content
            this.elements.loading.style.display = 'none';
            this.elements.mainContent.style.display = 'block';
            
        } catch (error) {
            console.error('Error initializing popup:', error);
            this.showError('Failed to initialize extension');
        }
    }

    setupEventListeners() {
        // Connect button
        this.elements.connectBtn.addEventListener('click', () => {
            this.handleConnect();
        });

        // Dashboard button
        this.elements.dashboardBtn.addEventListener('click', () => {
            this.openDashboard();
        });

        // Refresh button
        this.elements.refreshBtn.addEventListener('click', () => {
            this.refreshStatus();
        });

        // Send message button
        this.elements.sendBtn.addEventListener('click', () => {
            this.sendQuickMessage();
        });

        // Message input validation
        this.elements.contactInput.addEventListener('input', () => {
            this.validateMessageForm();
        });

        this.elements.messageInput.addEventListener('input', () => {
            this.validateMessageForm();
        });

        // Settings link
        this.elements.settingsLink.addEventListener('click', (e) => {
            e.preventDefault();
            this.openSettings();
        });
    }

    async getCurrentTab() {
        return new Promise((resolve) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                resolve(tabs[0]);
            });
        });
    }

    async loadStatus() {
        try {
            if (this.isWhatsAppTab) {
                // Get status from content script
                const response = await this.sendMessageToTab({ action: 'getStatus' });
                this.accountStatus = response;
                this.updateStatusDisplay();
            } else {
                // Get status from background script
                const response = await this.sendMessageToBackground({ action: 'getAccounts' });
                this.updateStatusFromAccounts(response?.accounts || []);
            }
            
            // Load statistics
            await this.loadStatistics();
            
        } catch (error) {
            console.error('Error loading status:', error);
            this.updateStatusDisplay({ isConnected: false, error: 'Failed to load status' });
        }
    }

    updateStatusDisplay(status = this.accountStatus) {
        if (!status) {
            this.elements.statusDot.className = 'status-dot disconnected';
            this.elements.statusText.textContent = 'Not connected';
            this.elements.accountInfo.textContent = this.isWhatsAppTab ? 
                'Please log in to WhatsApp Web' : 
                'Open WhatsApp Web to connect';
            this.elements.connectBtn.textContent = 'Open WhatsApp Web';
            this.elements.connectBtn.disabled = false;
            return;
        }

        if (status.isConnected) {
            this.elements.statusDot.className = 'status-dot connected';
            this.elements.statusText.textContent = 'Connected';
            this.elements.accountInfo.textContent = status.accountInfo?.phone || 'Connected to WhatsApp';
            this.elements.connectBtn.textContent = 'Disconnect';
        } else {
            this.elements.statusDot.className = 'status-dot disconnected';
            this.elements.statusText.textContent = 'Disconnected';
            this.elements.accountInfo.textContent = status.error || 'Not connected to WhatsApp';
            this.elements.connectBtn.textContent = this.isWhatsAppTab ? 'Reconnect' : 'Open WhatsApp Web';
        }

        this.elements.connectBtn.disabled = false;
        this.validateMessageForm();
    }

    updateStatusFromAccounts(accounts) {
        if (accounts.length === 0) {
            this.updateStatusDisplay(null);
            return;
        }

        const connectedAccounts = accounts.filter(acc => acc.isConnected);
        if (connectedAccounts.length > 0) {
            this.updateStatusDisplay({
                isConnected: true,
                accountInfo: {
                    phone: connectedAccounts[0].phone
                }
            });
        } else {
            this.updateStatusDisplay({
                isConnected: false,
                error: `${accounts.length} account(s) found but not connected`
            });
        }
    }

    async loadStatistics() {
        try {
            // Get statistics from background script or storage
            const stats = await this.getStatistics();
            this.elements.messagesCount.textContent = stats.messagesCount || '0';
            this.elements.conversationsCount.textContent = stats.conversationsCount || '0';
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    async getStatistics() {
        // Get statistics from Chrome storage or background script
        return new Promise((resolve) => {
            chrome.storage.local.get(['dailyStats'], (result) => {
                const today = new Date().toDateString();
                const stats = result.dailyStats?.[today] || { messagesCount: 0, conversationsCount: 0 };
                resolve(stats);
            });
        });
    }

    async handleConnect() {
        try {
            this.elements.connectBtn.disabled = true;
            this.elements.statusDot.className = 'status-dot connecting';
            this.elements.statusText.textContent = 'Connecting...';

            if (this.isWhatsAppTab) {
                if (this.accountStatus?.isConnected) {
                    // Disconnect
                    await this.disconnect();
                } else {
                    // Reconnect
                    await this.connect();
                }
            } else {
                // Open WhatsApp Web
                await this.openWhatsAppWeb();
            }
        } catch (error) {
            console.error('Error handling connect:', error);
            this.showError('Failed to connect to WhatsApp');
        } finally {
            this.elements.connectBtn.disabled = false;
        }
    }

    async connect() {
        // Trigger connection in content script
        const response = await this.sendMessageToTab({ action: 'connect' });
        if (response?.success) {
            this.showSuccess('Connected to WhatsApp successfully');
            await this.loadStatus();
        } else {
            throw new Error('Failed to connect');
        }
    }

    async disconnect() {
        // Trigger disconnection
        const response = await this.sendMessageToTab({ action: 'disconnect' });
        if (response?.success) {
            this.showSuccess('Disconnected from WhatsApp');
            await this.loadStatus();
        } else {
            throw new Error('Failed to disconnect');
        }
    }

    async openWhatsAppWeb() {
        chrome.tabs.create({ url: 'https://web.whatsapp.com' });
        window.close();
    }

    async refreshStatus() {
        this.elements.refreshBtn.disabled = true;
        this.elements.refreshBtn.textContent = 'Refreshing...';
        
        try {
            await this.loadStatus();
            this.showSuccess('Status refreshed');
        } catch (error) {
            this.showError('Failed to refresh status');
        } finally {
            this.elements.refreshBtn.disabled = false;
            this.elements.refreshBtn.textContent = 'Refresh Status';
        }
    }

    validateMessageForm() {
        const hasContact = this.elements.contactInput.value.trim().length > 0;
        const hasMessage = this.elements.messageInput.value.trim().length > 0;
        const isConnected = this.accountStatus?.isConnected || false;
        
        this.elements.sendBtn.disabled = !(hasContact && hasMessage && isConnected);
    }

    async sendQuickMessage() {
        const contact = this.elements.contactInput.value.trim();
        const message = this.elements.messageInput.value.trim();

        if (!contact || !message) {
            this.showError('Please enter both contact and message');
            return;
        }

        try {
            this.elements.sendBtn.disabled = true;
            this.elements.sendBtn.textContent = 'Sending...';

            let response;
            if (this.isWhatsAppTab) {
                response = await this.sendMessageToTab({
                    action: 'sendMessage',
                    contactName: contact,
                    message: message
                });
            } else {
                response = await this.sendMessageToBackground({
                    action: 'sendMessage',
                    contactName: contact,
                    message: message
                });
            }

            if (response?.success) {
                this.showSuccess('Message sent successfully');
                this.elements.messageInput.value = '';
                this.validateMessageForm();
                
                // Update statistics
                await this.updateMessageStats();
            } else {
                throw new Error('Failed to send message');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            this.showError('Failed to send message');
        } finally {
            this.elements.sendBtn.disabled = false;
            this.elements.sendBtn.textContent = 'Send Message';
        }
    }

    async updateMessageStats() {
        const today = new Date().toDateString();
        const result = await new Promise(resolve => {
            chrome.storage.local.get(['dailyStats'], resolve);
        });
        
        const dailyStats = result.dailyStats || {};
        const todayStats = dailyStats[today] || { messagesCount: 0, conversationsCount: 0 };
        
        todayStats.messagesCount += 1;
        dailyStats[today] = todayStats;
        
        await chrome.storage.local.set({ dailyStats });
        await this.loadStatistics();
    }

    openDashboard() {
        chrome.tabs.create({ url: 'http://localhost:5173/apps/whatsapp' });
        window.close();
    }

    openSettings() {
        chrome.tabs.create({ url: 'http://localhost:5173/apps/whatsapp' });
        window.close();
    }

    async sendMessageToTab(message) {
        return new Promise((resolve, reject) => {
            if (!this.currentTab?.id) {
                reject(new Error('No active tab'));
                return;
            }

            chrome.tabs.sendMessage(this.currentTab.id, message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    async sendMessageToBackground(message) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    showError(message) {
        this.elements.errorMessage.textContent = message;
        this.elements.errorMessage.style.display = 'block';
        this.elements.successMessage.style.display = 'none';
        
        setTimeout(() => {
            this.elements.errorMessage.style.display = 'none';
        }, 5000);
    }

    showSuccess(message) {
        this.elements.successMessage.textContent = message;
        this.elements.successMessage.style.display = 'block';
        this.elements.errorMessage.style.display = 'none';
        
        setTimeout(() => {
            this.elements.successMessage.style.display = 'none';
        }, 3000);
    }
}
