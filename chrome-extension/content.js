// WhatsApp Web Content Script
// This script runs in the context of WhatsApp Web and captures messages/conversations

console.log('WhatsApp Business Connector: Content script loaded');

class WhatsAppConnector {
  constructor() {
    this.isConnected = false;
    this.accountInfo = null;
    this.conversations = new Map();
    this.messages = new Map();
    this.observers = [];
    this.apiEndpoint = 'http://localhost:3001/api/whatsapp'; // Your backend API
    
    this.init();
  }

  async init() {
    // Wait for WhatsApp Web to load
    await this.waitForWhatsAppLoad();
    
    // Get account information
    this.accountInfo = await this.getAccountInfo();
    
    // Set up observers for real-time data capture
    this.setupObservers();
    
    // Send initial data to backend
    this.syncInitialData();
    
    console.log('WhatsApp Connector initialized', this.accountInfo);
  }

  async waitForWhatsAppLoad() {
    return new Promise((resolve) => {
      const checkLoad = () => {
        // Check if main WhatsApp elements are loaded
        const chatList = document.querySelector('[data-testid="chat-list"]');
        const header = document.querySelector('header[data-testid="chatlist-header"]');
        
        if (chatList && header) {
          resolve();
        } else {
          setTimeout(checkLoad, 1000);
        }
      };
      checkLoad();
    });
  }

  async getAccountInfo() {
    try {
      // Extract account information from WhatsApp Web
      const profilePic = document.querySelector('[data-testid="default-user"] img')?.src;
      const phoneNumber = this.extractPhoneNumber();
      
      return {
        phone: phoneNumber,
        profilePicture: profilePic,
        isConnected: true,
        lastSeen: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting account info:', error);
      return null;
    }
  }

  extractPhoneNumber() {
    // Try to extract phone number from various places in WhatsApp Web
    try {
      // Method 1: From profile section
      const profileSection = document.querySelector('[data-testid="default-user"]');
      if (profileSection) {
        const phoneSpan = profileSection.querySelector('span[title*="+"]');
        if (phoneSpan) return phoneSpan.title;
      }
      
      // Method 2: From settings or other locations
      // This might need adjustment based on WhatsApp Web updates
      return null;
    } catch (error) {
      console.error('Error extracting phone number:', error);
      return null;
    }
  }

  setupObservers() {
    // Observer for new messages
    this.observeMessages();
    
    // Observer for conversation list changes
    this.observeConversations();
    
    // Observer for typing indicators
    this.observeTypingIndicators();
  }

  observeMessages() {
    const messageContainer = document.querySelector('[data-testid="conversation-panel-messages"]');
    if (!messageContainer) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const messageElements = node.querySelectorAll('[data-testid="msg-container"]');
            messageElements.forEach((msgElement) => {
              this.processMessage(msgElement);
            });
          }
        });
      });
    });

    observer.observe(messageContainer, {
      childList: true,
      subtree: true
    });

    this.observers.push(observer);
  }

  observeConversations() {
    const chatList = document.querySelector('[data-testid="chat-list"]');
    if (!chatList) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const conversationElements = node.querySelectorAll('[data-testid="list-item-"]');
            conversationElements.forEach((convElement) => {
              this.processConversation(convElement);
            });
          }
        });
      });
    });

    observer.observe(chatList, {
      childList: true,
      subtree: true
    });

    this.observers.push(observer);
  }

  observeTypingIndicators() {
    // Observe typing indicators and online status
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.target.textContent?.includes('typing')) {
          this.handleTypingIndicator(mutation.target);
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      characterData: true
    });

    this.observers.push(observer);
  }

  processMessage(messageElement) {
    try {
      const messageData = this.extractMessageData(messageElement);
      if (messageData) {
        this.messages.set(messageData.id, messageData);
        this.sendToBackend('message', messageData);
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  }

  extractMessageData(messageElement) {
    try {
      // Extract message information from DOM element
      const messageId = messageElement.getAttribute('data-id') || Date.now().toString();
      const isFromMe = messageElement.classList.contains('message-out');
      
      // Get message content
      const contentElement = messageElement.querySelector('[data-testid="conversation-text"]') ||
                           messageElement.querySelector('.selectable-text');
      const content = contentElement?.textContent || '';
      
      // Get timestamp
      const timeElement = messageElement.querySelector('[data-testid="msg-meta"] span');
      const timestamp = this.parseTimestamp(timeElement?.textContent);
      
      // Get message status (for outgoing messages)
      let status = 'sent';
      if (isFromMe) {
        const statusElement = messageElement.querySelector('[data-testid="msg-meta"] span[data-testid*="status"]');
        if (statusElement) {
          if (statusElement.querySelector('[data-testid="msg-dblcheck-ack"]')) {
            status = statusElement.classList.contains('read') ? 'read' : 'delivered';
          }
        }
      }
      
      // Determine message type
      let messageType = 'text';
      if (messageElement.querySelector('[data-testid="image-thumb"]')) messageType = 'image';
      else if (messageElement.querySelector('[data-testid="audio-thumb"]')) messageType = 'audio';
      else if (messageElement.querySelector('[data-testid="document-thumb"]')) messageType = 'document';
      
      // Get current conversation info
      const conversationHeader = document.querySelector('[data-testid="conversation-header"]');
      const contactName = conversationHeader?.querySelector('span[title]')?.title || 'Unknown';
      const contactPhone = this.extractContactPhone(conversationHeader);
      
      return {
        id: messageId,
        content: content,
        timestamp: timestamp,
        type: messageType,
        isFromMe: isFromMe,
        status: status,
        contactName: contactName,
        contactPhone: contactPhone,
        whatsappMessageId: messageId
      };
    } catch (error) {
      console.error('Error extracting message data:', error);
      return null;
    }
  }

  processConversation(conversationElement) {
    try {
      const conversationData = this.extractConversationData(conversationElement);
      if (conversationData) {
        this.conversations.set(conversationData.id, conversationData);
        this.sendToBackend('conversation', conversationData);
      }
    } catch (error) {
      console.error('Error processing conversation:', error);
    }
  }

  extractConversationData(conversationElement) {
    try {
      const nameElement = conversationElement.querySelector('[data-testid="list-item-"] span[title]');
      const name = nameElement?.title || 'Unknown';
      
      const lastMessageElement = conversationElement.querySelector('.matched-text, ._21Ahp span');
      const lastMessage = lastMessageElement?.textContent || '';
      
      const timeElement = conversationElement.querySelector('._15G96');
      const lastMessageTime = this.parseTimestamp(timeElement?.textContent);
      
      const unreadElement = conversationElement.querySelector('._38M1B');
      const unreadCount = unreadElement ? parseInt(unreadElement.textContent) || 0 : 0;
      
      const profilePic = conversationElement.querySelector('img')?.src;
      
      return {
        id: Date.now().toString() + Math.random(),
        contactName: name,
        lastMessage: lastMessage,
        lastMessageTime: lastMessageTime,
        unreadCount: unreadCount,
        profilePicture: profilePic,
        isPinned: conversationElement.classList.contains('pinned'),
        isArchived: false
      };
    } catch (error) {
      console.error('Error extracting conversation data:', error);
      return null;
    }
  }

  parseTimestamp(timeString) {
    if (!timeString) return new Date();
    
    // Handle different time formats from WhatsApp
    const now = new Date();
    
    if (timeString.includes(':')) {
      // Time format like "14:30"
      const [hours, minutes] = timeString.split(':');
      const date = new Date();
      date.setHours(parseInt(hours), parseInt(minutes), 0, 0);
      return date;
    } else if (timeString.includes('/')) {
      // Date format like "12/25/2023"
      return new Date(timeString);
    }
    
    return now;
  }

  extractContactPhone(headerElement) {
    // Try to extract phone number from conversation header
    try {
      const phoneElement = headerElement?.querySelector('span[title*="+"]');
      return phoneElement?.title || null;
    } catch (error) {
      return null;
    }
  }

  handleTypingIndicator(element) {
    // Handle typing indicators
    const conversationHeader = document.querySelector('[data-testid="conversation-header"]');
    const contactName = conversationHeader?.querySelector('span[title]')?.title;
    
    if (contactName) {
      this.sendToBackend('typing', {
        contactName: contactName,
        isTyping: element.textContent.includes('typing'),
        timestamp: new Date().toISOString()
      });
    }
  }

  async sendToBackend(type, data) {
    try {
      // Send data to your backend API
      const payload = {
        type: type,
        data: data,
        accountInfo: this.accountInfo,
        timestamp: new Date().toISOString()
      };

      // Use Chrome extension messaging to send to background script
      chrome.runtime.sendMessage({
        action: 'sendToBackend',
        payload: payload
      });

    } catch (error) {
      console.error('Error sending to backend:', error);
    }
  }

  async syncInitialData() {
    // Sync existing conversations and recent messages
    try {
      const conversations = this.getAllConversations();
      for (const conversation of conversations) {
        this.sendToBackend('conversation', conversation);
      }
    } catch (error) {
      console.error('Error syncing initial data:', error);
    }
  }

  getAllConversations() {
    const conversationElements = document.querySelectorAll('[data-testid="list-item-"]');
    const conversations = [];
    
    conversationElements.forEach((element) => {
      const conversationData = this.extractConversationData(element);
      if (conversationData) {
        conversations.push(conversationData);
      }
    });
    
    return conversations;
  }

  // Method to send messages (called from popup or background script)
  async sendMessage(contactName, message) {
    try {
      // Find and click on the conversation
      const conversationElement = this.findConversationByName(contactName);
      if (conversationElement) {
        conversationElement.click();
        
        // Wait for conversation to load
        await this.sleep(1000);
        
        // Find message input and send message
        const messageInput = document.querySelector('[data-testid="conversation-compose-box-input"]');
        if (messageInput) {
          messageInput.textContent = message;
          
          // Trigger input event
          const inputEvent = new Event('input', { bubbles: true });
          messageInput.dispatchEvent(inputEvent);
          
          // Find and click send button
          const sendButton = document.querySelector('[data-testid="compose-btn-send"]');
          if (sendButton) {
            sendButton.click();
            return true;
          }
        }
      }
      return false;
    } catch (error) {
      console.error('Error sending message:', error);
      return false;
    }
  }

  findConversationByName(name) {
    const conversationElements = document.querySelectorAll('[data-testid="list-item-"]');
    for (const element of conversationElements) {
      const nameElement = element.querySelector('span[title]');
      if (nameElement && nameElement.title === name) {
        return element;
      }
    }
    return null;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  destroy() {
    // Clean up observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Initialize the connector
let whatsappConnector;

// Wait for page to be fully loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    whatsappConnector = new WhatsAppConnector();
  });
} else {
  whatsappConnector = new WhatsAppConnector();
}

// Listen for messages from popup or background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'sendMessage') {
    whatsappConnector.sendMessage(request.contactName, request.message)
      .then(success => sendResponse({ success }));
    return true; // Keep message channel open for async response
  }
  
  if (request.action === 'getStatus') {
    sendResponse({
      isConnected: whatsappConnector?.isConnected || false,
      accountInfo: whatsappConnector?.accountInfo || null
    });
  }
});

// Clean up on page unload
window.addEventListener('beforeunload', () => {
  if (whatsappConnector) {
    whatsappConnector.destroy();
  }
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WhatsAppConnector;
}
