<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Business Connector</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 350px;
            min-height: 400px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
        }

        .header {
            background: #25d366;
            color: white;
            padding: 16px;
            text-align: center;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .header p {
            font-size: 12px;
            opacity: 0.9;
        }

        .content {
            padding: 16px;
        }

        .status-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-dot.connected {
            background: #25d366;
        }

        .status-dot.disconnected {
            background: #dc3545;
        }

        .status-dot.connecting {
            background: #ffc107;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .status-text {
            font-size: 14px;
            font-weight: 500;
        }

        .account-info {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        .actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #25d366;
            color: white;
        }

        .btn-primary:hover {
            background: #20b358;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-outline {
            background: transparent;
            color: #25d366;
            border: 1px solid #25d366;
        }

        .btn-outline:hover {
            background: #25d366;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .quick-message {
            margin-top: 16px;
        }

        .quick-message h3 {
            font-size: 14px;
            margin-bottom: 8px;
            color: #333;
        }

        .message-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            margin-bottom: 8px;
            resize: vertical;
            min-height: 60px;
        }

        .contact-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-top: 16px;
        }

        .stat-item {
            background: white;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 18px;
            font-weight: 600;
            color: #25d366;
        }

        .stat-label {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
        }

        .footer {
            padding: 12px 16px;
            border-top: 1px solid #eee;
            background: white;
            text-align: center;
        }

        .footer a {
            color: #25d366;
            text-decoration: none;
            font-size: 12px;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #25d366;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            margin-bottom: 12px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            margin-bottom: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>WhatsApp Business</h1>
        <p>Connector Extension</p>
    </div>

    <div class="content">
        <div id="loading" class="loading">
            <div class="spinner"></div>
        </div>

        <div id="main-content" style="display: none;">
            <div class="status-card">
                <div class="status-indicator">
                    <div id="status-dot" class="status-dot disconnected"></div>
                    <div>
                        <div id="status-text" class="status-text">Checking connection...</div>
                        <div id="account-info" class="account-info"></div>
                    </div>
                </div>
            </div>

            <div id="error-message" class="error" style="display: none;"></div>
            <div id="success-message" class="success" style="display: none;"></div>

            <div class="actions">
                <button id="connect-btn" class="btn btn-primary">Connect to WhatsApp</button>
                <button id="dashboard-btn" class="btn btn-outline">Open Dashboard</button>
                <button id="refresh-btn" class="btn btn-secondary">Refresh Status</button>
            </div>

            <div class="quick-message">
                <h3>Quick Message</h3>
                <input type="text" id="contact-input" class="contact-input" placeholder="Contact name or number">
                <textarea id="message-input" class="message-input" placeholder="Type your message..."></textarea>
                <button id="send-btn" class="btn btn-primary" disabled>Send Message</button>
            </div>

            <div class="stats">
                <div class="stat-item">
                    <div id="messages-count" class="stat-number">0</div>
                    <div class="stat-label">Messages Today</div>
                </div>
                <div class="stat-item">
                    <div id="conversations-count" class="stat-number">0</div>
                    <div class="stat-label">Active Chats</div>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <a href="#" id="settings-link">Settings & Configuration</a>
    </div>

    <script src="popup.js"></script>
</body>
</html>
