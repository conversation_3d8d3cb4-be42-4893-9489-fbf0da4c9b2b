// Background Script for WhatsApp Business Connector
// Handles communication between content script and backend API

console.log('WhatsApp Business Connector: Background script loaded');

class BackgroundService {
  constructor() {
    this.apiEndpoint = 'http://localhost:3001/api/whatsapp';
    this.accounts = new Map();
    this.isConnected = false;
    
    this.init();
  }

  init() {
    // Set up message listeners
    this.setupMessageListeners();
    
    // Set up tab listeners
    this.setupTabListeners();
    
    // Set up periodic sync
    this.setupPeriodicSync();
    
    console.log('Background service initialized');
  }

  setupMessageListeners() {
    // Listen for messages from content scripts
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Listen for external messages (from web app)
    chrome.runtime.onMessageExternal.addListener((request, sender, sendResponse) => {
      this.handleExternalMessage(request, sender, sendResponse);
      return true;
    });
  }

  setupTabListeners() {
    // Monitor WhatsApp Web tabs
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (tab.url && tab.url.includes('web.whatsapp.com') && changeInfo.status === 'complete') {
        this.handleWhatsAppTabReady(tabId, tab);
      }
    });

    // Handle tab removal
    chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
      this.handleTabRemoved(tabId);
    });
  }

  setupPeriodicSync() {
    // Sync data every 30 seconds
    setInterval(() => {
      this.syncAccountStatus();
    }, 30000);
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'sendToBackend':
          await this.sendToBackend(request.payload, sender.tab);
          sendResponse({ success: true });
          break;

        case 'getAccountStatus':
          const status = await this.getAccountStatus(sender.tab?.id);
          sendResponse({ status });
          break;

        case 'sendMessage':
          const result = await this.sendMessageToWhatsApp(request.contactName, request.message, sender.tab?.id);
          sendResponse({ success: result });
          break;

        case 'getConversations':
          const conversations = await this.getConversations(sender.tab?.id);
          sendResponse({ conversations });
          break;

        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message });
    }
  }

  async handleExternalMessage(request, sender, sendResponse) {
    try {
      // Handle messages from the web application
      switch (request.action) {
        case 'sendMessage':
          const result = await this.sendMessageFromWebApp(request.accountId, request.contactName, request.message);
          sendResponse({ success: result });
          break;

        case 'getAccounts':
          const accounts = Array.from(this.accounts.values());
          sendResponse({ accounts });
          break;

        case 'getAccountStatus':
          const status = await this.getAccountStatusByPhone(request.phone);
          sendResponse({ status });
          break;

        default:
          sendResponse({ error: 'Unknown external action' });
      }
    } catch (error) {
      console.error('Error handling external message:', error);
      sendResponse({ error: error.message });
    }
  }

  async sendToBackend(payload, tab) {
    try {
      // Add tab information to payload
      const enrichedPayload = {
        ...payload,
        tabId: tab?.id,
        tabUrl: tab?.url,
        timestamp: new Date().toISOString()
      };

      // Send to backend API
      const response = await fetch(`${this.apiEndpoint}/webhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(enrichedPayload)
      });

      if (!response.ok) {
        throw new Error(`Backend API error: ${response.status}`);
      }

      const result = await response.json();
      console.log('Data sent to backend:', result);

      // Update local account info if this is account data
      if (payload.type === 'account' && payload.accountInfo) {
        this.updateAccountInfo(tab.id, payload.accountInfo);
      }

      return result;
    } catch (error) {
      console.error('Error sending to backend:', error);
      
      // Store data locally if backend is unavailable
      await this.storeDataLocally(payload);
      throw error;
    }
  }

  async storeDataLocally(data) {
    try {
      // Store data in Chrome storage for later sync
      const key = `pending_${Date.now()}`;
      await chrome.storage.local.set({ [key]: data });
      console.log('Data stored locally for later sync');
    } catch (error) {
      console.error('Error storing data locally:', error);
    }
  }

  async syncPendingData() {
    try {
      // Get all pending data from local storage
      const storage = await chrome.storage.local.get();
      const pendingKeys = Object.keys(storage).filter(key => key.startsWith('pending_'));

      for (const key of pendingKeys) {
        try {
          await this.sendToBackend(storage[key]);
          await chrome.storage.local.remove(key);
          console.log(`Synced pending data: ${key}`);
        } catch (error) {
          console.error(`Failed to sync pending data ${key}:`, error);
        }
      }
    } catch (error) {
      console.error('Error syncing pending data:', error);
    }
  }

  updateAccountInfo(tabId, accountInfo) {
    this.accounts.set(tabId, {
      ...accountInfo,
      tabId: tabId,
      lastUpdate: new Date().toISOString()
    });
  }

  async handleWhatsAppTabReady(tabId, tab) {
    console.log('WhatsApp tab ready:', tabId);
    
    // Inject content script if not already injected
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content.js']
      });
    } catch (error) {
      console.error('Error injecting content script:', error);
    }
  }

  handleTabRemoved(tabId) {
    // Clean up account info when tab is closed
    if (this.accounts.has(tabId)) {
      console.log('WhatsApp tab closed:', tabId);
      this.accounts.delete(tabId);
    }
  }

  async getAccountStatus(tabId) {
    if (!tabId) return null;
    
    try {
      // Get status from content script
      const response = await chrome.tabs.sendMessage(tabId, { action: 'getStatus' });
      return response;
    } catch (error) {
      console.error('Error getting account status:', error);
      return null;
    }
  }

  async getAccountStatusByPhone(phone) {
    // Find account by phone number
    for (const [tabId, account] of this.accounts) {
      if (account.phone === phone) {
        return await this.getAccountStatus(tabId);
      }
    }
    return null;
  }

  async sendMessageToWhatsApp(contactName, message, tabId) {
    if (!tabId) return false;
    
    try {
      const response = await chrome.tabs.sendMessage(tabId, {
        action: 'sendMessage',
        contactName: contactName,
        message: message
      });
      
      return response?.success || false;
    } catch (error) {
      console.error('Error sending message to WhatsApp:', error);
      return false;
    }
  }

  async sendMessageFromWebApp(accountId, contactName, message) {
    // Find the appropriate tab for the account
    const account = Array.from(this.accounts.values()).find(acc => acc.id === accountId);
    if (!account) {
      throw new Error('Account not found or not connected');
    }

    return await this.sendMessageToWhatsApp(contactName, message, account.tabId);
  }

  async getConversations(tabId) {
    if (!tabId) return [];
    
    try {
      const response = await chrome.tabs.sendMessage(tabId, { action: 'getConversations' });
      return response?.conversations || [];
    } catch (error) {
      console.error('Error getting conversations:', error);
      return [];
    }
  }

  async syncAccountStatus() {
    // Sync status of all connected accounts
    for (const [tabId, account] of this.accounts) {
      try {
        const status = await this.getAccountStatus(tabId);
        if (status) {
          // Send status update to backend
          await this.sendToBackend({
            type: 'status_update',
            data: status,
            accountInfo: account
          }, { id: tabId });
        }
      } catch (error) {
        console.error(`Error syncing account status for tab ${tabId}:`, error);
      }
    }

    // Try to sync any pending data
    await this.syncPendingData();
  }

  // Method to handle installation
  async handleInstall() {
    console.log('Extension installed');
    
    // Set up initial configuration
    await chrome.storage.local.set({
      apiEndpoint: this.apiEndpoint,
      isConfigured: false,
      accounts: []
    });
  }

  // Method to handle updates
  async handleUpdate() {
    console.log('Extension updated');
    
    // Migrate data if needed
    await this.migrateData();
  }

  async migrateData() {
    // Handle data migration between extension versions
    try {
      const storage = await chrome.storage.local.get();
      // Add migration logic here if needed
      console.log('Data migration completed');
    } catch (error) {
      console.error('Error during data migration:', error);
    }
  }
}

// Initialize background service
const backgroundService = new BackgroundService();

// Handle extension lifecycle events
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    backgroundService.handleInstall();
  } else if (details.reason === 'update') {
    backgroundService.handleUpdate();
  }
});

// Handle extension startup
chrome.runtime.onStartup.addListener(() => {
  console.log('Extension started');
});

// Keep service worker alive
chrome.runtime.onSuspend.addListener(() => {
  console.log('Extension suspending');
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BackgroundService;
}
