-- Create products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    product_type VARCHAR(50) NOT NULL DEFAULT 'physical',
    sku VARCHAR(255),
    description TEXT,
    weight DECIMAL(10,4),
    width DECIMAL(10,4),
    depth DECIMAL(10,4),
    height DECIMAL(10,4),
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    cost_price DECIMAL(10,2),
    retail_price DECIMAL(10,2),
    sale_price DECIMAL(10,2),
    map_price DECIMAL(10,2),
    tax_class_id INTEGER,
    product_tax_code VARCHAR(255),
    brand_id BIGINT,
    brand_name VARCHAR(255),
    inventory_level INTEGER NOT NULL DEFAULT 0,
    inventory_warning_level INTEGER,
    inventory_tracking VARCHAR(50) NOT NULL DEFAULT 'none',
    fixed_cost_shipping_price DECIMAL(10,2),
    is_free_shipping BOOLEAN NOT NULL DEFAULT false,
    is_visible BOOLEAN NOT NULL DEFAULT true,
    is_featured BOOLEAN NOT NULL DEFAULT false,
    warranty TEXT,
    bin_picking_number VARCHAR(255),
    layout_file VARCHAR(255),
    upc VARCHAR(255),
    search_keywords TEXT,
    availability_description TEXT,
    availability VARCHAR(50) NOT NULL DEFAULT 'available',
    gift_wrapping_options_type VARCHAR(50) NOT NULL DEFAULT 'any',
    sort_order INTEGER,
    condition VARCHAR(50) NOT NULL DEFAULT 'New',
    is_condition_shown BOOLEAN NOT NULL DEFAULT true,
    order_quantity_minimum INTEGER,
    order_quantity_maximum INTEGER,
    page_title VARCHAR(255),
    meta_description TEXT,
    view_count INTEGER NOT NULL DEFAULT 0,
    preorder_release_date TIMESTAMP WITH TIME ZONE,
    preorder_message TEXT,
    is_preorder_only BOOLEAN NOT NULL DEFAULT false,
    is_price_hidden BOOLEAN NOT NULL DEFAULT false,
    price_hidden_label VARCHAR(255),
    open_graph_type VARCHAR(50) NOT NULL DEFAULT 'product',
    open_graph_title VARCHAR(255),
    open_graph_description TEXT,
    open_graph_use_meta_description BOOLEAN NOT NULL DEFAULT true,
    open_graph_use_product_name BOOLEAN NOT NULL DEFAULT true,
    open_graph_use_image BOOLEAN NOT NULL DEFAULT true,
    gtin VARCHAR(255),
    mpn VARCHAR(255),
    date_last_imported TIMESTAMP WITH TIME ZONE,
    reviews_rating_sum INTEGER,
    reviews_count INTEGER,
    total_sold INTEGER NOT NULL DEFAULT 0,
    bigcommerce_id BIGINT,
    sync_status VARCHAR(50) NOT NULL DEFAULT 'not_synced',
    last_sync_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create product categories table
CREATE TABLE product_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES product_categories(id) ON DELETE SET NULL,
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_visible BOOLEAN NOT NULL DEFAULT true,
    bigcommerce_id BIGINT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create product category mappings table
CREATE TABLE product_category_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES product_categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(product_id, category_id)
);

-- Create product images table
CREATE TABLE product_images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    is_thumbnail BOOLEAN NOT NULL DEFAULT false,
    sort_order INTEGER NOT NULL DEFAULT 0,
    description TEXT,
    bigcommerce_id BIGINT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create product videos table
CREATE TABLE product_videos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    video_type VARCHAR(50) NOT NULL,
    video_id VARCHAR(255) NOT NULL,
    length VARCHAR(50),
    bigcommerce_id BIGINT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create product variants table
CREATE TABLE product_variants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    sku VARCHAR(255),
    sku_id BIGINT,
    cost_price DECIMAL(10,2),
    price DECIMAL(10,2),
    sale_price DECIMAL(10,2),
    retail_price DECIMAL(10,2),
    map_price DECIMAL(10,2),
    weight DECIMAL(10,4),
    width DECIMAL(10,4),
    height DECIMAL(10,4),
    depth DECIMAL(10,4),
    is_free_shipping BOOLEAN NOT NULL DEFAULT false,
    fixed_cost_shipping_price DECIMAL(10,2),
    purchasing_disabled BOOLEAN NOT NULL DEFAULT false,
    purchasing_disabled_message TEXT,
    image_url TEXT,
    upc VARCHAR(255),
    inventory_level INTEGER NOT NULL DEFAULT 0,
    inventory_warning_level INTEGER,
    bin_picking_number VARCHAR(255),
    mpn VARCHAR(255),
    gtin VARCHAR(255),
    calculated_price DECIMAL(10,2),
    calculated_weight DECIMAL(10,4),
    bigcommerce_id BIGINT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create variant option values table
CREATE TABLE variant_option_values (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    variant_id UUID NOT NULL REFERENCES product_variants(id) ON DELETE CASCADE,
    option_id BIGINT NOT NULL,
    option_display_name VARCHAR(255) NOT NULL,
    label VARCHAR(255) NOT NULL,
    bigcommerce_id BIGINT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create product custom fields table
CREATE TABLE product_custom_fields (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    value TEXT NOT NULL,
    bigcommerce_id BIGINT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create bulk pricing rules table
CREATE TABLE bulk_pricing_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    quantity_min INTEGER NOT NULL,
    quantity_max INTEGER,
    rule_type VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    bigcommerce_id BIGINT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create supplier product shares table
CREATE TABLE supplier_product_shares (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    shared_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    is_active BOOLEAN NOT NULL DEFAULT true,
    permissions TEXT NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(product_id, supplier_id)
);

-- Create indexes for better performance
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_inventory_level ON products(inventory_level);
CREATE INDEX idx_products_is_visible ON products(is_visible);
CREATE INDEX idx_products_is_featured ON products(is_featured);
CREATE INDEX idx_products_sync_status ON products(sync_status);
CREATE INDEX idx_products_bigcommerce_id ON products(bigcommerce_id);
CREATE INDEX idx_products_created_at ON products(created_at);
CREATE INDEX idx_products_updated_at ON products(updated_at);

CREATE INDEX idx_product_categories_name ON product_categories(name);
CREATE INDEX idx_product_categories_parent_id ON product_categories(parent_id);
CREATE INDEX idx_product_categories_sort_order ON product_categories(sort_order);

CREATE INDEX idx_product_category_mappings_product_id ON product_category_mappings(product_id);
CREATE INDEX idx_product_category_mappings_category_id ON product_category_mappings(category_id);

CREATE INDEX idx_product_images_product_id ON product_images(product_id);
CREATE INDEX idx_product_images_is_thumbnail ON product_images(is_thumbnail);
CREATE INDEX idx_product_images_sort_order ON product_images(sort_order);

CREATE INDEX idx_product_videos_product_id ON product_videos(product_id);
CREATE INDEX idx_product_videos_sort_order ON product_videos(sort_order);

CREATE INDEX idx_product_variants_product_id ON product_variants(product_id);
CREATE INDEX idx_product_variants_sku ON product_variants(sku);
CREATE INDEX idx_product_variants_price ON product_variants(price);

CREATE INDEX idx_variant_option_values_variant_id ON variant_option_values(variant_id);
CREATE INDEX idx_variant_option_values_option_id ON variant_option_values(option_id);

CREATE INDEX idx_product_custom_fields_product_id ON product_custom_fields(product_id);
CREATE INDEX idx_product_custom_fields_name ON product_custom_fields(name);

CREATE INDEX idx_bulk_pricing_rules_product_id ON bulk_pricing_rules(product_id);
CREATE INDEX idx_bulk_pricing_rules_quantity_min ON bulk_pricing_rules(quantity_min);

CREATE INDEX idx_supplier_product_shares_product_id ON supplier_product_shares(product_id);
CREATE INDEX idx_supplier_product_shares_supplier_id ON supplier_product_shares(supplier_id);
CREATE INDEX idx_supplier_product_shares_is_active ON supplier_product_shares(is_active);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_categories_updated_at BEFORE UPDATE ON product_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_images_updated_at BEFORE UPDATE ON product_images
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_videos_updated_at BEFORE UPDATE ON product_videos
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_variants_updated_at BEFORE UPDATE ON product_variants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_custom_fields_updated_at BEFORE UPDATE ON product_custom_fields
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bulk_pricing_rules_updated_at BEFORE UPDATE ON bulk_pricing_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_supplier_product_shares_updated_at BEFORE UPDATE ON supplier_product_shares
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
