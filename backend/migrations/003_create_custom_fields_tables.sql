-- Create custom field definitions table
CREATE TABLE custom_field_definitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    label VARCHAR(255) NOT NULL,
    key VARCHAR(255) NOT NULL,
    field_type VARCHAR(50) NOT NULL, -- text, number, single_select, multi_select, date, boolean, textarea, url, email
    resource_type VARCHAR(50) NOT NULL, -- products, customers, orders, companies, brands, categories, etc.
    namespace VARCHAR(255) NOT NULL DEFAULT 'global', -- global, form_specific
    description TEXT,
    placeholder_text VARCHAR(255),
    help_text VARCHAR(255),
    is_required BOOLEAN NOT NULL DEFAULT false,
    is_searchable BOOLEAN NOT NULL DEFAULT false,
    is_filterable BOOLEAN NOT NULL DEFAULT false,
    is_ai_enabled BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    sort_order INTEGER NOT NULL DEFAULT 0,
    validation_rules JSONB DEFAULT '{}', -- min/max length, regex patterns, etc.
    field_options JSONB DEFAULT '{}', -- for select fields: options array, for other fields: additional config
    webhook_config JSONB DEFAULT '{}', -- webhook integration settings
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(key, resource_type, namespace)
);

-- Create index for performance
CREATE INDEX idx_custom_field_definitions_resource_type ON custom_field_definitions(resource_type);
CREATE INDEX idx_custom_field_definitions_namespace ON custom_field_definitions(namespace);
CREATE INDEX idx_custom_field_definitions_active ON custom_field_definitions(is_active);

-- Create custom field values table for storing actual field values
CREATE TABLE custom_field_values (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    field_definition_id UUID NOT NULL REFERENCES custom_field_definitions(id) ON DELETE CASCADE,
    resource_id UUID NOT NULL, -- ID of the product, customer, order, etc.
    resource_type VARCHAR(50) NOT NULL, -- products, customers, orders, etc.
    value TEXT, -- Store all values as text, convert as needed
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(field_definition_id, resource_id)
);

-- Create indexes for performance
CREATE INDEX idx_custom_field_values_resource ON custom_field_values(resource_type, resource_id);
CREATE INDEX idx_custom_field_values_field_definition ON custom_field_values(field_definition_id);

-- Create custom field resource mappings table (for form-specific fields)
CREATE TABLE custom_field_resource_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    field_definition_id UUID NOT NULL REFERENCES custom_field_definitions(id) ON DELETE CASCADE,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID, -- NULL for global mappings, specific ID for form-specific
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_custom_field_resource_mappings_resource ON custom_field_resource_mappings(resource_type, resource_id);
CREATE INDEX idx_custom_field_resource_mappings_field ON custom_field_resource_mappings(field_definition_id);

-- Create webhook integrations table for third-party sync
CREATE TABLE custom_field_webhook_integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    field_definition_id UUID NOT NULL REFERENCES custom_field_definitions(id) ON DELETE CASCADE,
    integration_name VARCHAR(255) NOT NULL,
    webhook_url TEXT NOT NULL,
    webhook_method VARCHAR(10) NOT NULL DEFAULT 'POST',
    webhook_headers JSONB DEFAULT '{}',
    field_mapping JSONB DEFAULT '{}', -- mapping between our field and external field
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status VARCHAR(50) DEFAULT 'pending', -- pending, success, error
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create index for performance
CREATE INDEX idx_custom_field_webhook_integrations_field ON custom_field_webhook_integrations(field_definition_id);
CREATE INDEX idx_custom_field_webhook_integrations_active ON custom_field_webhook_integrations(is_active);

-- Insert some sample global custom fields
INSERT INTO custom_field_definitions (label, key, field_type, resource_type, namespace, description, placeholder_text, help_text, is_searchable, is_filterable) VALUES
('SEO Title', 'seo_title', 'text', 'products', 'global', 'Custom SEO title for search engines', 'Enter SEO title...', 'Custom SEO title for search engines', true, false),
('Warranty Period', 'warranty_period', 'number', 'products', 'global', 'Warranty period in months', '12', 'Warranty period in months', false, true),
('Priority Level', 'priority_level', 'single_select', 'customers', 'global', 'Customer priority level', '', 'Customer priority classification', false, true);

-- Insert field options for select fields
UPDATE custom_field_definitions 
SET field_options = '{"options": [{"value": "low", "label": "Low"}, {"value": "medium", "label": "Medium"}, {"value": "high", "label": "High"}, {"value": "vip", "label": "VIP"}]}'
WHERE key = 'priority_level';

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_custom_field_definitions_updated_at BEFORE UPDATE ON custom_field_definitions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_custom_field_values_updated_at BEFORE UPDATE ON custom_field_values FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_custom_field_resource_mappings_updated_at BEFORE UPDATE ON custom_field_resource_mappings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_custom_field_webhook_integrations_updated_at BEFORE UPDATE ON custom_field_webhook_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
