use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use validator::Validate;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Product {
    pub id: Uuid,
    pub name: String,
    pub product_type: String, // physical, digital
    pub sku: Option<String>,
    pub description: Option<String>,
    pub weight: Option<f64>,
    pub width: Option<f64>,
    pub depth: Option<f64>,
    pub height: Option<f64>,
    pub price: f64,
    pub cost_price: Option<f64>,
    pub retail_price: Option<f64>,
    pub sale_price: Option<f64>,
    pub map_price: Option<f64>,
    pub tax_class_id: Option<i32>,
    pub product_tax_code: Option<String>,
    pub brand_id: Option<i64>,
    pub brand_name: Option<String>,
    pub inventory_level: i32,
    pub inventory_warning_level: Option<i32>,
    pub inventory_tracking: String, // none, simple, variant
    pub fixed_cost_shipping_price: Option<f64>,
    pub is_free_shipping: bool,
    pub is_visible: bool,
    pub is_featured: bool,
    pub warranty: Option<String>,
    pub bin_picking_number: Option<String>,
    pub layout_file: Option<String>,
    pub upc: Option<String>,
    pub search_keywords: Option<String>,
    pub availability_description: Option<String>,
    pub availability: String, // available, disabled, preorder
    pub gift_wrapping_options_type: String, // any, none, list
    pub sort_order: Option<i32>,
    pub condition: String, // New, Used, Refurbished
    pub is_condition_shown: bool,
    pub order_quantity_minimum: Option<i32>,
    pub order_quantity_maximum: Option<i32>,
    pub page_title: Option<String>,
    pub meta_description: Option<String>,
    pub view_count: i32,
    pub preorder_release_date: Option<DateTime<Utc>>,
    pub preorder_message: Option<String>,
    pub is_preorder_only: bool,
    pub is_price_hidden: bool,
    pub price_hidden_label: Option<String>,
    pub open_graph_type: String, // product, article, website
    pub open_graph_title: Option<String>,
    pub open_graph_description: Option<String>,
    pub open_graph_use_meta_description: bool,
    pub open_graph_use_product_name: bool,
    pub open_graph_use_image: bool,
    pub gtin: Option<String>,
    pub mpn: Option<String>,
    pub date_last_imported: Option<DateTime<Utc>>,
    pub reviews_rating_sum: Option<i32>,
    pub reviews_count: Option<i32>,
    pub total_sold: i32,
    pub bigcommerce_id: Option<i64>,
    pub sync_status: String, // synced, pending, error, not_synced
    pub last_sync_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct ProductImage {
    pub id: Uuid,
    pub product_id: Uuid,
    pub image_url: String,
    pub is_thumbnail: bool,
    pub sort_order: i32,
    pub description: Option<String>,
    pub bigcommerce_id: Option<i64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct ProductVideo {
    pub id: Uuid,
    pub product_id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub sort_order: i32,
    pub video_type: String, // youtube, vimeo, mp4
    pub video_id: String,
    pub length: Option<String>,
    pub bigcommerce_id: Option<i64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct ProductVariant {
    pub id: Uuid,
    pub product_id: Uuid,
    pub sku: Option<String>,
    pub sku_id: Option<i64>,
    pub cost_price: Option<f64>,
    pub price: Option<f64>,
    pub sale_price: Option<f64>,
    pub retail_price: Option<f64>,
    pub map_price: Option<f64>,
    pub weight: Option<f64>,
    pub width: Option<f64>,
    pub height: Option<f64>,
    pub depth: Option<f64>,
    pub is_free_shipping: bool,
    pub fixed_cost_shipping_price: Option<f64>,
    pub purchasing_disabled: bool,
    pub purchasing_disabled_message: Option<String>,
    pub image_url: Option<String>,
    pub upc: Option<String>,
    pub inventory_level: i32,
    pub inventory_warning_level: Option<i32>,
    pub bin_picking_number: Option<String>,
    pub mpn: Option<String>,
    pub gtin: Option<String>,
    pub calculated_price: Option<f64>,
    pub calculated_weight: Option<f64>,
    pub bigcommerce_id: Option<i64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct ProductCategory {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub parent_id: Option<Uuid>,
    pub sort_order: i32,
    pub is_visible: bool,
    pub bigcommerce_id: Option<i64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct ProductCategoryMapping {
    pub id: Uuid,
    pub product_id: Uuid,
    pub category_id: Uuid,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct ProductCustomField {
    pub id: Uuid,
    pub product_id: Uuid,
    pub name: String,
    pub value: String,
    pub bigcommerce_id: Option<i64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct BulkPricingRule {
    pub id: Uuid,
    pub product_id: Uuid,
    pub quantity_min: i32,
    pub quantity_max: Option<i32>,
    pub rule_type: String, // price, percent, fixed
    pub amount: f64,
    pub bigcommerce_id: Option<i64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct VariantOptionValue {
    pub id: Uuid,
    pub variant_id: Uuid,
    pub option_id: i64,
    pub option_display_name: String,
    pub label: String,
    pub bigcommerce_id: Option<i64>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct SupplierProductShare {
    pub id: Uuid,
    pub product_id: Uuid,
    pub supplier_id: Uuid, // Reference to supplier user/organization
    pub shared_at: DateTime<Utc>,
    pub is_active: bool,
    pub permissions: String, // JSON string with permissions
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateProductRequest {
    #[validate(length(min = 1, max = 255))]
    pub name: String,
    pub product_type: String,
    pub sku: Option<String>,
    pub description: Option<String>,
    pub weight: Option<f64>,
    pub width: Option<f64>,
    pub depth: Option<f64>,
    pub height: Option<f64>,
    #[validate(range(min = 0.0))]
    pub price: f64,
    pub cost_price: Option<f64>,
    pub retail_price: Option<f64>,
    pub sale_price: Option<f64>,
    pub map_price: Option<f64>,
    pub tax_class_id: Option<i32>,
    pub product_tax_code: Option<String>,
    pub brand_id: Option<i64>,
    pub brand_name: Option<String>,
    pub inventory_level: i32,
    pub inventory_warning_level: Option<i32>,
    pub inventory_tracking: String,
    pub fixed_cost_shipping_price: Option<f64>,
    pub is_free_shipping: bool,
    pub is_visible: bool,
    pub is_featured: bool,
    pub warranty: Option<String>,
    pub bin_picking_number: Option<String>,
    pub layout_file: Option<String>,
    pub upc: Option<String>,
    pub search_keywords: Option<String>,
    pub availability_description: Option<String>,
    pub availability: String,
    pub gift_wrapping_options_type: String,
    pub sort_order: Option<i32>,
    pub condition: String,
    pub is_condition_shown: bool,
    pub order_quantity_minimum: Option<i32>,
    pub order_quantity_maximum: Option<i32>,
    pub page_title: Option<String>,
    pub meta_description: Option<String>,
    pub preorder_release_date: Option<DateTime<Utc>>,
    pub preorder_message: Option<String>,
    pub is_preorder_only: bool,
    pub is_price_hidden: bool,
    pub price_hidden_label: Option<String>,
    pub open_graph_type: String,
    pub open_graph_title: Option<String>,
    pub open_graph_description: Option<String>,
    pub open_graph_use_meta_description: bool,
    pub open_graph_use_product_name: bool,
    pub open_graph_use_image: bool,
    pub gtin: Option<String>,
    pub mpn: Option<String>,
    pub categories: Option<Vec<Uuid>>,
    pub images: Option<Vec<CreateProductImageRequest>>,
    pub videos: Option<Vec<CreateProductVideoRequest>>,
    pub variants: Option<Vec<CreateProductVariantRequest>>,
    pub custom_fields: Option<Vec<CreateCustomFieldRequest>>,
    pub bulk_pricing_rules: Option<Vec<CreateBulkPricingRuleRequest>>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateProductImageRequest {
    pub image_url: String,
    pub is_thumbnail: bool,
    pub sort_order: i32,
    pub description: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateProductVideoRequest {
    pub title: String,
    pub description: Option<String>,
    pub sort_order: i32,
    pub video_type: String,
    pub video_id: String,
    pub length: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateProductVariantRequest {
    pub sku: Option<String>,
    pub cost_price: Option<f64>,
    pub price: Option<f64>,
    pub sale_price: Option<f64>,
    pub retail_price: Option<f64>,
    pub map_price: Option<f64>,
    pub weight: Option<f64>,
    pub width: Option<f64>,
    pub height: Option<f64>,
    pub depth: Option<f64>,
    pub is_free_shipping: bool,
    pub fixed_cost_shipping_price: Option<f64>,
    pub purchasing_disabled: bool,
    pub purchasing_disabled_message: Option<String>,
    pub image_url: Option<String>,
    pub upc: Option<String>,
    pub inventory_level: i32,
    pub inventory_warning_level: Option<i32>,
    pub bin_picking_number: Option<String>,
    pub mpn: Option<String>,
    pub gtin: Option<String>,
    pub option_values: Option<Vec<CreateVariantOptionValueRequest>>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateVariantOptionValueRequest {
    pub option_id: i64,
    pub option_display_name: String,
    pub label: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateCustomFieldRequest {
    pub name: String,
    pub value: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateBulkPricingRuleRequest {
    pub quantity_min: i32,
    pub quantity_max: Option<i32>,
    pub rule_type: String,
    pub amount: f64,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateProductRequest {
    pub name: Option<String>,
    pub product_type: Option<String>,
    pub sku: Option<String>,
    pub description: Option<String>,
    pub weight: Option<f64>,
    pub width: Option<f64>,
    pub depth: Option<f64>,
    pub height: Option<f64>,
    pub price: Option<f64>,
    pub cost_price: Option<f64>,
    pub retail_price: Option<f64>,
    pub sale_price: Option<f64>,
    pub map_price: Option<f64>,
    pub tax_class_id: Option<i32>,
    pub product_tax_code: Option<String>,
    pub brand_id: Option<i64>,
    pub brand_name: Option<String>,
    pub inventory_level: Option<i32>,
    pub inventory_warning_level: Option<i32>,
    pub inventory_tracking: Option<String>,
    pub fixed_cost_shipping_price: Option<f64>,
    pub is_free_shipping: Option<bool>,
    pub is_visible: Option<bool>,
    pub is_featured: Option<bool>,
    pub warranty: Option<String>,
    pub bin_picking_number: Option<String>,
    pub layout_file: Option<String>,
    pub upc: Option<String>,
    pub search_keywords: Option<String>,
    pub availability_description: Option<String>,
    pub availability: Option<String>,
    pub gift_wrapping_options_type: Option<String>,
    pub sort_order: Option<i32>,
    pub condition: Option<String>,
    pub is_condition_shown: Option<bool>,
    pub order_quantity_minimum: Option<i32>,
    pub order_quantity_maximum: Option<i32>,
    pub page_title: Option<String>,
    pub meta_description: Option<String>,
    pub preorder_release_date: Option<DateTime<Utc>>,
    pub preorder_message: Option<String>,
    pub is_preorder_only: Option<bool>,
    pub is_price_hidden: Option<bool>,
    pub price_hidden_label: Option<String>,
    pub open_graph_type: Option<String>,
    pub open_graph_title: Option<String>,
    pub open_graph_description: Option<String>,
    pub open_graph_use_meta_description: Option<bool>,
    pub open_graph_use_product_name: Option<bool>,
    pub open_graph_use_image: Option<bool>,
    pub gtin: Option<String>,
    pub mpn: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ProductResponse {
    pub id: Uuid,
    pub name: String,
    pub product_type: String,
    pub sku: Option<String>,
    pub description: Option<String>,
    pub weight: Option<f64>,
    pub width: Option<f64>,
    pub depth: Option<f64>,
    pub height: Option<f64>,
    pub price: f64,
    pub cost_price: Option<f64>,
    pub retail_price: Option<f64>,
    pub sale_price: Option<f64>,
    pub map_price: Option<f64>,
    pub tax_class_id: Option<i32>,
    pub product_tax_code: Option<String>,
    pub brand_id: Option<i64>,
    pub brand_name: Option<String>,
    pub inventory_level: i32,
    pub inventory_warning_level: Option<i32>,
    pub inventory_tracking: String,
    pub fixed_cost_shipping_price: Option<f64>,
    pub is_free_shipping: bool,
    pub is_visible: bool,
    pub is_featured: bool,
    pub warranty: Option<String>,
    pub bin_picking_number: Option<String>,
    pub layout_file: Option<String>,
    pub upc: Option<String>,
    pub search_keywords: Option<String>,
    pub availability_description: Option<String>,
    pub availability: String,
    pub gift_wrapping_options_type: String,
    pub sort_order: Option<i32>,
    pub condition: String,
    pub is_condition_shown: bool,
    pub order_quantity_minimum: Option<i32>,
    pub order_quantity_maximum: Option<i32>,
    pub page_title: Option<String>,
    pub meta_description: Option<String>,
    pub view_count: i32,
    pub preorder_release_date: Option<DateTime<Utc>>,
    pub preorder_message: Option<String>,
    pub is_preorder_only: bool,
    pub is_price_hidden: bool,
    pub price_hidden_label: Option<String>,
    pub open_graph_type: String,
    pub open_graph_title: Option<String>,
    pub open_graph_description: Option<String>,
    pub open_graph_use_meta_description: bool,
    pub open_graph_use_product_name: bool,
    pub open_graph_use_image: bool,
    pub gtin: Option<String>,
    pub mpn: Option<String>,
    pub date_last_imported: Option<DateTime<Utc>>,
    pub reviews_rating_sum: Option<i32>,
    pub reviews_count: Option<i32>,
    pub total_sold: i32,
    pub bigcommerce_id: Option<i64>,
    pub sync_status: String,
    pub last_sync_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub categories: Vec<ProductCategoryResponse>,
    pub images: Vec<ProductImageResponse>,
    pub videos: Vec<ProductVideoResponse>,
    pub variants: Vec<ProductVariantResponse>,
    pub custom_fields: Vec<ProductCustomFieldResponse>,
    pub bulk_pricing_rules: Vec<BulkPricingRuleResponse>,
}

#[derive(Debug, Serialize)]
pub struct ProductListResponse {
    pub id: Uuid,
    pub name: String,
    pub sku: Option<String>,
    pub price: f64,
    pub inventory_level: i32,
    pub is_visible: bool,
    pub is_featured: bool,
    pub sync_status: String,
    pub last_sync_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct ProductImageResponse {
    pub id: Uuid,
    pub image_url: String,
    pub is_thumbnail: bool,
    pub sort_order: i32,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct ProductVideoResponse {
    pub id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub sort_order: i32,
    pub video_type: String,
    pub video_id: String,
    pub length: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct ProductVariantResponse {
    pub id: Uuid,
    pub sku: Option<String>,
    pub cost_price: Option<f64>,
    pub price: Option<f64>,
    pub sale_price: Option<f64>,
    pub retail_price: Option<f64>,
    pub map_price: Option<f64>,
    pub weight: Option<f64>,
    pub width: Option<f64>,
    pub height: Option<f64>,
    pub depth: Option<f64>,
    pub is_free_shipping: bool,
    pub fixed_cost_shipping_price: Option<f64>,
    pub purchasing_disabled: bool,
    pub purchasing_disabled_message: Option<String>,
    pub image_url: Option<String>,
    pub upc: Option<String>,
    pub inventory_level: i32,
    pub inventory_warning_level: Option<i32>,
    pub bin_picking_number: Option<String>,
    pub mpn: Option<String>,
    pub gtin: Option<String>,
    pub calculated_price: Option<f64>,
    pub calculated_weight: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub option_values: Vec<VariantOptionValueResponse>,
}

#[derive(Debug, Serialize)]
pub struct ProductCategoryResponse {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub parent_id: Option<Uuid>,
    pub sort_order: i32,
    pub is_visible: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct ProductCustomFieldResponse {
    pub id: Uuid,
    pub name: String,
    pub value: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct BulkPricingRuleResponse {
    pub id: Uuid,
    pub quantity_min: i32,
    pub quantity_max: Option<i32>,
    pub rule_type: String,
    pub amount: f64,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct VariantOptionValueResponse {
    pub id: Uuid,
    pub option_id: i64,
    pub option_display_name: String,
    pub label: String,
    pub created_at: DateTime<Utc>,
}

impl From<Product> for ProductResponse {
    fn from(product: Product) -> Self {
        ProductResponse {
            id: product.id,
            name: product.name,
            product_type: product.product_type,
            sku: product.sku,
            description: product.description,
            weight: product.weight,
            width: product.width,
            depth: product.depth,
            height: product.height,
            price: product.price,
            cost_price: product.cost_price,
            retail_price: product.retail_price,
            sale_price: product.sale_price,
            map_price: product.map_price,
            tax_class_id: product.tax_class_id,
            product_tax_code: product.product_tax_code,
            brand_id: product.brand_id,
            brand_name: product.brand_name,
            inventory_level: product.inventory_level,
            inventory_warning_level: product.inventory_warning_level,
            inventory_tracking: product.inventory_tracking,
            fixed_cost_shipping_price: product.fixed_cost_shipping_price,
            is_free_shipping: product.is_free_shipping,
            is_visible: product.is_visible,
            is_featured: product.is_featured,
            warranty: product.warranty,
            bin_picking_number: product.bin_picking_number,
            layout_file: product.layout_file,
            upc: product.upc,
            search_keywords: product.search_keywords,
            availability_description: product.availability_description,
            availability: product.availability,
            gift_wrapping_options_type: product.gift_wrapping_options_type,
            sort_order: product.sort_order,
            condition: product.condition,
            is_condition_shown: product.is_condition_shown,
            order_quantity_minimum: product.order_quantity_minimum,
            order_quantity_maximum: product.order_quantity_maximum,
            page_title: product.page_title,
            meta_description: product.meta_description,
            view_count: product.view_count,
            preorder_release_date: product.preorder_release_date,
            preorder_message: product.preorder_message,
            is_preorder_only: product.is_preorder_only,
            is_price_hidden: product.is_price_hidden,
            price_hidden_label: product.price_hidden_label,
            open_graph_type: product.open_graph_type,
            open_graph_title: product.open_graph_title,
            open_graph_description: product.open_graph_description,
            open_graph_use_meta_description: product.open_graph_use_meta_description,
            open_graph_use_product_name: product.open_graph_use_product_name,
            open_graph_use_image: product.open_graph_use_image,
            gtin: product.gtin,
            mpn: product.mpn,
            date_last_imported: product.date_last_imported,
            reviews_rating_sum: product.reviews_rating_sum,
            reviews_count: product.reviews_count,
            total_sold: product.total_sold,
            bigcommerce_id: product.bigcommerce_id,
            sync_status: product.sync_status,
            last_sync_at: product.last_sync_at,
            created_at: product.created_at,
            updated_at: product.updated_at,
            categories: Vec::new(), // Will be populated separately
            images: Vec::new(),     // Will be populated separately
            videos: Vec::new(),     // Will be populated separately
            variants: Vec::new(),   // Will be populated separately
            custom_fields: Vec::new(), // Will be populated separately
            bulk_pricing_rules: Vec::new(), // Will be populated separately
        }
    }
}

impl From<Product> for ProductListResponse {
    fn from(product: Product) -> Self {
        ProductListResponse {
            id: product.id,
            name: product.name,
            sku: product.sku,
            price: product.price,
            inventory_level: product.inventory_level,
            is_visible: product.is_visible,
            is_featured: product.is_featured,
            sync_status: product.sync_status,
            last_sync_at: product.last_sync_at,
            created_at: product.created_at,
            updated_at: product.updated_at,
        }
    }
}
