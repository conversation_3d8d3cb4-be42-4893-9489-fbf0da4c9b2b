use axum::{
    routing::{get, post, put, delete},
    Router,
};
use std::net::SocketAddr;
use tower_http::cors::CorsLayer;
use tracing_subscriber;

mod config;
mod handlers;
mod models;
mod services;

use config::Config;
use handlers::{health, users};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::init();

    // Load configuration
    let config = Config::from_env()?;

    // Initialize database connections
    let db_pool = services::database::init_postgres(&config.database_url).await?;
    let mongo_client = services::database::init_mongodb(&config.mongodb_url).await?;
    let redis_client = services::database::init_redis(&config.redis_url).await?;

    // Create application state
    let app_state = services::AppState {
        db_pool,
        mongo_client,
        redis_client,
        config: config.clone(),
    };

    // Build our application with routes
    let app = Router::new()
        .route("/", get(health::root))
        .route("/health", get(health::health_check))
        .route("/api/users", get(users::list_users).post(users::create_user))
        .route("/api/users/:id", get(users::get_user))
        .route("/api/products", get(handlers::products::list_products).post(handlers::products::create_product))
        .route("/api/products/:id", get(handlers::products::get_product).put(handlers::products::update_product).delete(handlers::products::delete_product))
        .route("/api/products/:id/sync", post(handlers::products::sync_with_bigcommerce))
        .route("/api/categories", get(handlers::products::list_categories))
        .route("/api/custom-fields", get(handlers::custom_fields::list_custom_field_definitions).post(handlers::custom_fields::create_custom_field_definition))
        .route("/api/custom-fields/:id", get(handlers::custom_fields::get_custom_field_definition).put(handlers::custom_fields::update_custom_field_definition).delete(handlers::custom_fields::delete_custom_field_definition))
        .layer(CorsLayer::permissive())
        .with_state(app_state);

    // Run the server
    let addr = SocketAddr::from(([127, 0, 0, 1], config.port));
    tracing::info!("Server running on http://{}", addr);

    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}
