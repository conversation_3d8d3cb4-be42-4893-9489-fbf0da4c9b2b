const { Builder, By, until, Key } = require('selenium-webdriver');
const chrome = require('selenium-webdriver/chrome');
const fs = require('fs').promises;
const path = require('path');

class SeleniumService {
  constructor() {
    this.sessions = new Map(); // Store active sessions by account ID
    this.sessionDir = path.join(__dirname, '../../sessions');
    this.ensureSessionDir();
  }

  async ensureSessionDir() {
    try {
      await fs.mkdir(this.sessionDir, { recursive: true });
    } catch (error) {
      console.error('Error creating session directory:', error);
    }
  }

  async startSession(accountId) {
    try {
      if (this.sessions.has(accountId)) {
        await this.stopSession(accountId);
      }

      const sessionPath = path.join(this.sessionDir, `session_${accountId}`);
      
      // Chrome options for WhatsApp Web
      const chromeOptions = new chrome.Options();
      chromeOptions.addArguments('--no-sandbox');
      chromeOptions.addArguments('--disable-dev-shm-usage');
      chromeOptions.addArguments('--disable-blink-features=AutomationControlled');
      chromeOptions.addArguments('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      chromeOptions.addArguments(`--user-data-dir=${sessionPath}`);
      chromeOptions.addArguments('--disable-web-security');
      chromeOptions.addArguments('--allow-running-insecure-content');
      
      // Disable images and CSS for faster loading (optional)
      const prefs = {
        'profile.managed_default_content_settings.images': 2,
        'profile.default_content_setting_values.notifications': 2
      };
      chromeOptions.setUserPreferences(prefs);

      const driver = await new Builder()
        .forBrowser('chrome')
        .setChromeOptions(chromeOptions)
        .build();

      // Navigate to WhatsApp Web
      await driver.get('https://web.whatsapp.com');
      
      // Store session
      this.sessions.set(accountId, {
        driver,
        sessionPath,
        startTime: new Date(),
        isConnected: false
      });

      console.log(`Selenium session started for account ${accountId}`);
      
      // Wait for QR code or main interface
      await this.waitForConnection(accountId);
      
      return { success: true, sessionId: accountId };
    } catch (error) {
      console.error('Error starting Selenium session:', error);
      return { success: false, error: error.message };
    }
  }

  async waitForConnection(accountId, timeout = 60000) {
    const session = this.sessions.get(accountId);
    if (!session) throw new Error('Session not found');

    const { driver } = session;
    
    try {
      // Wait for either QR code or main chat interface
      await driver.wait(until.elementLocated(
        By.css('[data-testid="qr-code"], [data-testid="chat-list"]')
      ), timeout);

      // Check if we're connected (chat list visible)
      const chatList = await driver.findElements(By.css('[data-testid="chat-list"]'));
      
      if (chatList.length > 0) {
        session.isConnected = true;
        console.log(`Account ${accountId} connected successfully`);
        return true;
      } else {
        console.log(`Account ${accountId} showing QR code`);
        return false;
      }
    } catch (error) {
      console.error('Error waiting for connection:', error);
      return false;
    }
  }

  async stopSession(accountId) {
    try {
      const session = this.sessions.get(accountId);
      if (session) {
        await session.driver.quit();
        this.sessions.delete(accountId);
        console.log(`Selenium session stopped for account ${accountId}`);
      }
      return { success: true };
    } catch (error) {
      console.error('Error stopping Selenium session:', error);
      return { success: false, error: error.message };
    }
  }

  async getQRCode(accountId) {
    try {
      const session = this.sessions.get(accountId);
      if (!session) return null;

      const { driver } = session;
      
      // Find QR code element
      const qrElement = await driver.findElement(By.css('[data-testid="qr-code"] img'));
      if (!qrElement) return null;

      // Get QR code as base64
      const qrCodeData = await qrElement.getAttribute('src');
      return qrCodeData;
    } catch (error) {
      console.error('Error getting QR code:', error);
      return null;
    }
  }

  async sendMessage(accountId, contactPhone, message, messageType = 'text') {
    try {
      const session = this.sessions.get(accountId);
      if (!session || !session.isConnected) {
        throw new Error('Session not found or not connected');
      }

      const { driver } = session;
      
      // Search for contact
      const searchBox = await driver.wait(
        until.elementLocated(By.css('[data-testid="chat-list-search"]')),
        10000
      );
      
      await searchBox.clear();
      await searchBox.sendKeys(contactPhone);
      await driver.sleep(2000);

      // Click on the contact
      const contactElement = await driver.wait(
        until.elementLocated(By.css('[data-testid="list-item-"]')),
        10000
      );
      await contactElement.click();

      // Wait for chat to load
      await driver.wait(
        until.elementLocated(By.css('[data-testid="conversation-compose-box-input"]')),
        10000
      );

      // Type message
      const messageBox = await driver.findElement(
        By.css('[data-testid="conversation-compose-box-input"]')
      );
      
      await messageBox.click();
      await messageBox.sendKeys(message);

      // Send message
      const sendButton = await driver.findElement(
        By.css('[data-testid="compose-btn-send"]')
      );
      await sendButton.click();

      // Wait for message to be sent
      await driver.sleep(1000);

      console.log(`Message sent to ${contactPhone}: ${message}`);
      return { success: true, messageId: Date.now().toString() };
    } catch (error) {
      console.error('Error sending message:', error);
      return { success: false, error: error.message };
    }
  }

  async bulkSendMessages(accountId, contacts, message, messageType = 'text') {
    const results = [];
    
    for (const contact of contacts) {
      try {
        const result = await this.sendMessage(accountId, contact.phone, message, messageType);
        results.push({
          contact: contact.phone,
          success: result.success,
          messageId: result.messageId,
          error: result.error
        });
        
        // Add delay between messages to avoid being blocked
        await this.sleep(2000 + Math.random() * 3000);
      } catch (error) {
        results.push({
          contact: contact.phone,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  async getContacts(accountId) {
    try {
      const session = this.sessions.get(accountId);
      if (!session || !session.isConnected) {
        throw new Error('Session not found or not connected');
      }

      const { driver } = session;
      
      // Get all chat list items
      const chatElements = await driver.findElements(
        By.css('[data-testid="list-item-"]')
      );

      const contacts = [];
      
      for (const element of chatElements) {
        try {
          const nameElement = await element.findElement(By.css('span[title]'));
          const name = await nameElement.getAttribute('title');
          
          // Try to get phone number (might not always be available)
          let phone = null;
          try {
            const phoneElement = await element.findElement(By.css('span[title*="+"]'));
            phone = await phoneElement.getAttribute('title');
          } catch (e) {
            // Phone number not visible, use name as identifier
            phone = name;
          }

          contacts.push({ name, phone });
        } catch (error) {
          // Skip this contact if we can't extract info
          continue;
        }
      }

      return contacts;
    } catch (error) {
      console.error('Error getting contacts:', error);
      return [];
    }
  }

  async getMessages(accountId, contactPhone, limit = 50) {
    try {
      const session = this.sessions.get(accountId);
      if (!session || !session.isConnected) {
        throw new Error('Session not found or not connected');
      }

      const { driver } = session;
      
      // Search and open conversation
      await this.openConversation(driver, contactPhone);
      
      // Get message elements
      const messageElements = await driver.findElements(
        By.css('[data-testid="msg-container"]')
      );

      const messages = [];
      
      for (const element of messageElements.slice(-limit)) {
        try {
          const isFromMe = await element.findElement(By.css('.message-out')).then(() => true).catch(() => false);
          
          const contentElement = await element.findElement(
            By.css('[data-testid="conversation-text"], .selectable-text')
          );
          const content = await contentElement.getText();
          
          const timeElement = await element.findElement(By.css('[data-testid="msg-meta"] span'));
          const time = await timeElement.getText();

          messages.push({
            content,
            time,
            isFromMe,
            timestamp: new Date()
          });
        } catch (error) {
          // Skip messages we can't parse
          continue;
        }
      }

      return messages;
    } catch (error) {
      console.error('Error getting messages:', error);
      return [];
    }
  }

  async openConversation(driver, contactPhone) {
    // Search for contact
    const searchBox = await driver.findElement(By.css('[data-testid="chat-list-search"]'));
    await searchBox.clear();
    await searchBox.sendKeys(contactPhone);
    await driver.sleep(2000);

    // Click on the contact
    const contactElement = await driver.wait(
      until.elementLocated(By.css('[data-testid="list-item-"]')),
      10000
    );
    await contactElement.click();

    // Wait for conversation to load
    await driver.wait(
      until.elementLocated(By.css('[data-testid="conversation-panel-messages"]')),
      10000
    );
  }

  async isSessionActive(accountId) {
    const session = this.sessions.get(accountId);
    if (!session) return false;

    try {
      // Check if driver is still responsive
      await session.driver.getTitle();
      return true;
    } catch (error) {
      // Session is dead, clean it up
      this.sessions.delete(accountId);
      return false;
    }
  }

  async getSessionStatus(accountId) {
    const session = this.sessions.get(accountId);
    if (!session) return { exists: false };

    return {
      exists: true,
      isConnected: session.isConnected,
      startTime: session.startTime,
      isActive: await this.isSessionActive(accountId)
    };
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Clean up all sessions
  async cleanup() {
    console.log('Cleaning up all Selenium sessions...');
    
    for (const [accountId, session] of this.sessions) {
      try {
        await session.driver.quit();
      } catch (error) {
        console.error(`Error cleaning up session ${accountId}:`, error);
      }
    }
    
    this.sessions.clear();
  }
}

module.exports = SeleniumService;
