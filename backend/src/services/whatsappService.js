const { Pool } = require('pg');
const { v4: uuidv4 } = require('uuid');

class WhatsAppService {
  constructor() {
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/nucleux_db'
    });
  }

  // Account Management
  async createAccount(accountData) {
    const { name, phone, organizationId, createdBy } = accountData;
    
    const query = `
      INSERT INTO whatsapp_accounts (name, phone, organization_id, created_by)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;
    
    const result = await this.pool.query(query, [name, phone, organizationId, createdBy]);
    return result.rows[0];
  }

  async getAccounts(organizationId) {
    const query = `
      SELECT wa.*, 
             COUNT(DISTINCT wc.id) as contact_count,
             COUNT(DISTINCT wconv.id) as conversation_count,
             COUNT(CASE WHEN wm.created_at >= CURRENT_DATE THEN 1 END) as messages_today
      FROM whatsapp_accounts wa
      LEFT JOIN whatsapp_contacts wc ON wa.id = wc.account_id
      LEFT JOIN whatsapp_conversations wconv ON wa.id = wconv.account_id
      LEFT JOIN whatsapp_messages wm ON wa.id = wm.account_id
      WHERE wa.organization_id = $1
      GROUP BY wa.id
      ORDER BY wa.created_at DESC
    `;
    
    const result = await this.pool.query(query, [organizationId]);
    return result.rows;
  }

  async getAccount(accountId, organizationId) {
    const query = `
      SELECT * FROM whatsapp_accounts 
      WHERE id = $1 AND organization_id = $2
    `;
    
    const result = await this.pool.query(query, [accountId, organizationId]);
    return result.rows[0];
  }

  async updateAccount(accountId, updateData, organizationId) {
    const allowedFields = ['name', 'status', 'settings', 'session_data', 'last_seen'];
    const updates = [];
    const values = [];
    let paramCount = 1;

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = $${paramCount}`);
        values.push(value);
        paramCount++;
      }
    }

    if (updates.length === 0) {
      throw new Error('No valid fields to update');
    }

    values.push(accountId, organizationId);
    
    const query = `
      UPDATE whatsapp_accounts 
      SET ${updates.join(', ')}, updated_at = NOW()
      WHERE id = $${paramCount} AND organization_id = $${paramCount + 1}
      RETURNING *
    `;
    
    const result = await this.pool.query(query, values);
    return result.rows[0];
  }

  async deleteAccount(accountId, organizationId) {
    const query = `
      DELETE FROM whatsapp_accounts 
      WHERE id = $1 AND organization_id = $2
    `;
    
    await this.pool.query(query, [accountId, organizationId]);
  }

  async updateAccountStatus(accountId, status) {
    const query = `
      UPDATE whatsapp_accounts 
      SET status = $1, last_seen = NOW(), updated_at = NOW()
      WHERE id = $2
      RETURNING *
    `;
    
    const result = await this.pool.query(query, [status, accountId]);
    return result.rows[0];
  }

  // Contact Management
  async createOrUpdateContact(accountId, contactData, organizationId) {
    const { phone, name, profile_picture_url, is_business, labels, custom_fields } = contactData;
    
    const query = `
      INSERT INTO whatsapp_contacts (account_id, phone, name, profile_picture_url, is_business, labels, custom_fields)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (account_id, phone) 
      DO UPDATE SET 
        name = EXCLUDED.name,
        profile_picture_url = EXCLUDED.profile_picture_url,
        is_business = EXCLUDED.is_business,
        labels = EXCLUDED.labels,
        custom_fields = EXCLUDED.custom_fields,
        updated_at = NOW()
      RETURNING *
    `;
    
    const result = await this.pool.query(query, [
      accountId, phone, name, profile_picture_url, is_business, labels, custom_fields
    ]);
    
    return result.rows[0];
  }

  async getContacts(accountId, organizationId, options = {}) {
    const { page = 1, limit = 50, search } = options;
    const offset = (page - 1) * limit;
    
    let query = `
      SELECT wc.*, 
             COUNT(wm.id) as message_count,
             MAX(wm.timestamp) as last_message_at
      FROM whatsapp_contacts wc
      LEFT JOIN whatsapp_messages wm ON wc.id = wm.contact_id
      WHERE wc.account_id = $1
    `;
    
    const params = [accountId];
    let paramCount = 2;
    
    if (search) {
      query += ` AND (wc.name ILIKE $${paramCount} OR wc.phone ILIKE $${paramCount})`;
      params.push(`%${search}%`);
      paramCount++;
    }
    
    query += `
      GROUP BY wc.id
      ORDER BY last_message_at DESC NULLS LAST, wc.name ASC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `;
    
    params.push(limit, offset);
    
    const result = await this.pool.query(query, params);
    return result.rows;
  }

  // Conversation Management
  async processConversation(conversationData, accountInfo) {
    const { contactName, contactPhone, lastMessage, unreadCount, isPinned } = conversationData;
    
    // First, ensure contact exists
    const contact = await this.createOrUpdateContact(accountInfo.accountId, {
      phone: contactPhone,
      name: contactName
    });
    
    // Create or update conversation
    const query = `
      INSERT INTO whatsapp_conversations (account_id, contact_id, is_pinned, unread_count, last_message_at)
      VALUES ($1, $2, $3, $4, NOW())
      ON CONFLICT (account_id, contact_id)
      DO UPDATE SET 
        is_pinned = EXCLUDED.is_pinned,
        unread_count = EXCLUDED.unread_count,
        last_message_at = EXCLUDED.last_message_at,
        updated_at = NOW()
      RETURNING *
    `;
    
    const result = await this.pool.query(query, [
      accountInfo.accountId, contact.id, isPinned, unreadCount
    ]);
    
    return result.rows[0];
  }

  async getConversations(accountId, organizationId, options = {}) {
    const { page = 1, limit = 20, search } = options;
    const offset = (page - 1) * limit;
    
    let query = `
      SELECT wconv.*, wc.name as contact_name, wc.phone as contact_phone, 
             wc.profile_picture_url, wc.is_online,
             wm.content as last_message_content, wm.timestamp as last_message_time,
             wm.is_from_me as last_message_from_me
      FROM whatsapp_conversations wconv
      JOIN whatsapp_contacts wc ON wconv.contact_id = wc.id
      LEFT JOIN whatsapp_messages wm ON wconv.id = wm.conversation_id 
        AND wm.timestamp = (
          SELECT MAX(timestamp) FROM whatsapp_messages 
          WHERE conversation_id = wconv.id
        )
      WHERE wconv.account_id = $1 AND wconv.is_archived = false
    `;
    
    const params = [accountId];
    let paramCount = 2;
    
    if (search) {
      query += ` AND (wc.name ILIKE $${paramCount} OR wc.phone ILIKE $${paramCount})`;
      params.push(`%${search}%`);
      paramCount++;
    }
    
    query += `
      ORDER BY wconv.is_pinned DESC, wconv.last_message_at DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `;
    
    params.push(limit, offset);
    
    const result = await this.pool.query(query, params);
    return result.rows;
  }

  // Message Management
  async processMessage(messageData, accountInfo) {
    const { 
      content, timestamp, type, isFromMe, status, contactName, contactPhone, whatsappMessageId 
    } = messageData;
    
    // Ensure contact exists
    const contact = await this.createOrUpdateContact(accountInfo.accountId, {
      phone: contactPhone,
      name: contactName
    });
    
    // Ensure conversation exists
    let conversation = await this.getOrCreateConversation(accountInfo.accountId, contact.id);
    
    // Store message
    const query = `
      INSERT INTO whatsapp_messages 
      (conversation_id, account_id, contact_id, whatsapp_message_id, content, message_type, 
       is_from_me, status, timestamp)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      ON CONFLICT (whatsapp_message_id) 
      DO UPDATE SET 
        content = EXCLUDED.content,
        status = EXCLUDED.status,
        updated_at = NOW()
      RETURNING *
    `;
    
    const result = await this.pool.query(query, [
      conversation.id, accountInfo.accountId, contact.id, whatsappMessageId,
      content, type, isFromMe, status, timestamp
    ]);
    
    // Update conversation last message time and unread count
    await this.updateConversationLastMessage(conversation.id, !isFromMe);
    
    return result.rows[0];
  }

  async getOrCreateConversation(accountId, contactId) {
    let query = `
      SELECT * FROM whatsapp_conversations 
      WHERE account_id = $1 AND contact_id = $2
    `;
    
    let result = await this.pool.query(query, [accountId, contactId]);
    
    if (result.rows.length === 0) {
      query = `
        INSERT INTO whatsapp_conversations (account_id, contact_id)
        VALUES ($1, $2)
        RETURNING *
      `;
      
      result = await this.pool.query(query, [accountId, contactId]);
    }
    
    return result.rows[0];
  }

  async updateConversationLastMessage(conversationId, incrementUnread = false) {
    const query = `
      UPDATE whatsapp_conversations 
      SET last_message_at = NOW(),
          unread_count = CASE WHEN $2 THEN unread_count + 1 ELSE unread_count END,
          updated_at = NOW()
      WHERE id = $1
    `;
    
    await this.pool.query(query, [conversationId, incrementUnread]);
  }

  async getMessages(conversationId, organizationId, options = {}) {
    const { page = 1, limit = 50 } = options;
    const offset = (page - 1) * limit;
    
    const query = `
      SELECT wm.*, wc.name as contact_name, wc.phone as contact_phone
      FROM whatsapp_messages wm
      JOIN whatsapp_conversations wconv ON wm.conversation_id = wconv.id
      JOIN whatsapp_contacts wc ON wm.contact_id = wc.id
      JOIN whatsapp_accounts wa ON wm.account_id = wa.id
      WHERE wm.conversation_id = $1 AND wa.organization_id = $2
      ORDER BY wm.timestamp ASC
      LIMIT $3 OFFSET $4
    `;
    
    const result = await this.pool.query(query, [conversationId, organizationId, limit, offset]);
    return result.rows;
  }

  async storeOutgoingMessage(messageData) {
    const { accountId, contactPhone, message, messageType, organizationId } = messageData;
    
    // Find or create contact
    const contact = await this.createOrUpdateContact(accountId, {
      phone: contactPhone,
      name: contactPhone // Use phone as name if name not provided
    });
    
    // Get or create conversation
    const conversation = await this.getOrCreateConversation(accountId, contact.id);
    
    // Store message
    const query = `
      INSERT INTO whatsapp_messages 
      (conversation_id, account_id, contact_id, content, message_type, is_from_me, status, timestamp)
      VALUES ($1, $2, $3, $4, $5, true, 'sent', NOW())
      RETURNING *
    `;
    
    const result = await this.pool.query(query, [
      conversation.id, accountId, contact.id, message, messageType
    ]);
    
    // Update conversation
    await this.updateConversationLastMessage(conversation.id, false);
    
    return result.rows[0];
  }

  // Analytics
  async getAnalytics(accountId, organizationId, options = {}) {
    const { startDate, endDate } = options;
    
    let query = `
      SELECT 
        DATE(wm.timestamp) as date,
        COUNT(*) as total_messages,
        COUNT(CASE WHEN wm.is_from_me THEN 1 END) as sent_messages,
        COUNT(CASE WHEN NOT wm.is_from_me THEN 1 END) as received_messages,
        COUNT(DISTINCT wm.contact_id) as unique_contacts
      FROM whatsapp_messages wm
      JOIN whatsapp_accounts wa ON wm.account_id = wa.id
      WHERE wm.account_id = $1 AND wa.organization_id = $2
    `;
    
    const params = [accountId, organizationId];
    let paramCount = 3;
    
    if (startDate) {
      query += ` AND wm.timestamp >= $${paramCount}`;
      params.push(startDate);
      paramCount++;
    }
    
    if (endDate) {
      query += ` AND wm.timestamp <= $${paramCount}`;
      params.push(endDate);
      paramCount++;
    }
    
    query += `
      GROUP BY DATE(wm.timestamp)
      ORDER BY date DESC
    `;
    
    const result = await this.pool.query(query, params);
    return result.rows;
  }

  // Typing Indicators
  async processTypingIndicator(typingData, accountInfo) {
    // Store typing indicator in cache or real-time system
    // This could be implemented with Redis for real-time updates
    console.log('Typing indicator:', typingData);
  }

  // Message Templates
  async getMessageTemplates(accountId, organizationId) {
    const query = `
      SELECT * FROM whatsapp_message_templates 
      WHERE account_id = $1 
      ORDER BY created_at DESC
    `;
    
    const result = await this.pool.query(query, [accountId]);
    return result.rows;
  }

  async createMessageTemplate(accountId, templateData, organizationId, createdBy) {
    const { name, category, language, template_data } = templateData;
    
    const query = `
      INSERT INTO whatsapp_message_templates 
      (account_id, name, category, language, template_data, created_by)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;
    
    const result = await this.pool.query(query, [
      accountId, name, category, language, template_data, createdBy
    ]);
    
    return result.rows[0];
  }
}

  // Utility method to close database connection
  async close() {
    await this.pool.end();
  }
}

module.exports = WhatsAppService;
