use mongodb::{Client as MongoClient, options::ClientOptions};
use redis::Client as RedisClient;
use sqlx::{postgres::PgPoolOptions, PgPool};
use std::time::Duration;

pub async fn init_postgres(database_url: &str) -> Result<PgPool, sqlx::Error> {
    tracing::info!("Connecting to PostgreSQL...");
    
    let pool = PgPoolOptions::new()
        .max_connections(10)
        .acquire_timeout(Duration::from_secs(30))
        .connect(database_url)
        .await?;

    // Run migrations if they exist
    sqlx::migrate!("./migrations").run(&pool).await.ok();
    
    tracing::info!("PostgreSQL connection established");
    Ok(pool)
}

pub async fn init_mongodb(mongodb_url: &str) -> Result<MongoClient, mongodb::error::Error> {
    tracing::info!("Connecting to MongoDB...");
    
    let client_options = ClientOptions::parse(mongodb_url).await?;
    let client = MongoClient::with_options(client_options)?;
    
    // Test the connection
    client
        .database("nucleux")
        .run_command(mongodb::bson::doc! {"ping": 1}, None)
        .await?;
    
    tracing::info!("MongoDB connection established");
    Ok(client)
}

pub async fn init_redis(redis_url: &str) -> Result<RedisClient, redis::RedisError> {
    tracing::info!("Connecting to Redis...");
    
    let client = RedisClient::open(redis_url)?;
    
    // Test the connection
    let mut conn = client.get_async_connection().await?;
    redis::cmd("PING").query_async::<_, String>(&mut conn).await?;
    
    tracing::info!("Redis connection established");
    Ok(client)
}
