use axum::{response::J<PERSON>, extract::State};
use serde_json::{json, Value};
use crate::services::AppState;

pub async fn root() -> Json<Value> {
    Json(json!({
        "message": "Welcome to Nucleux API",
        "version": "0.1.0",
        "status": "running"
    }))
}

pub async fn health_check(State(state): State<AppState>) -> Json<Value> {
    // Test database connections
    let postgres_status = match sqlx::query("SELECT 1").fetch_one(&state.db_pool).await {
        Ok(_) => "healthy",
        Err(_) => "unhealthy",
    };

    // let mongodb_status = match state
    //     .mongo_client
    //     .database("nucleux")
    //     .run_command(mongodb::bson::doc! {"ping": 1}, None)
    //     .await
    // {
    //     Ok(_) => "healthy",
    //     Err(_) => "unhealthy",
    // };

    // let redis_status = match state.redis_client.get_async_connection().await {
    //     Ok(_) => "healthy",
    //     Err(_) => "unhealthy",
    // };

    Json(json!({
        "status": "ok",
        "timestamp": chrono::Utc::now(),
        "services": {
            "postgres": postgres_status,
            // "mongodb": mongodb_status,
            // "redis": redis_status
        }
    }))
}
