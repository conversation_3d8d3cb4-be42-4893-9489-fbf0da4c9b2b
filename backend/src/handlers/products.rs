use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use uuid::Uuid;
use validator::Validate;

use crate::{
    models::{
        CreateProductRequest, Product, ProductListResponse, ProductResponse,
        UpdateProductRequest, ProductImage, ProductVideo, ProductVariant,
        ProductCategory, ProductCustomField, BulkPricingRule, VariantOptionValue,
        ProductImageResponse, ProductVideoResponse, ProductVariantResponse,
        ProductCategoryResponse, ProductCustomFieldResponse, BulkPricingRuleResponse,
        VariantOptionValueResponse
    },
    services::AppState,
};

#[derive(Debug, Deserialize)]
pub struct ProductQueryParams {
    pub page: Option<i64>,
    pub limit: Option<i64>,
    pub search: Option<String>,
    pub category: Option<Uuid>,
    pub is_visible: Option<bool>,
    pub is_featured: Option<bool>,
    pub sync_status: Option<String>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ProductListResult {
    pub products: Vec<ProductListResponse>,
    pub total: i64,
    pub page: i64,
    pub limit: i64,
    pub total_pages: i64,
}

pub async fn list_products(
    State(state): State<AppState>,
    Query(params): Query<ProductQueryParams>,
) -> Result<Json<ProductListResult>, StatusCode> {
    let page = params.page.unwrap_or(1).max(1);
    let limit = params.limit.unwrap_or(20).min(100).max(1);
    let offset = (page - 1) * limit;

    // Build the base query
    let mut query = "SELECT * FROM products WHERE 1=1".to_string();
    let mut count_query = "SELECT COUNT(*) FROM products WHERE 1=1".to_string();
    let mut query_params: Vec<String> = Vec::new();

    // Add search filter
    if let Some(search) = &params.search {
        if !search.trim().is_empty() {
            query.push_str(&format!(" AND (name ILIKE '%{}%' OR sku ILIKE '%{}%' OR description ILIKE '%{}%')", search, search, search));
            count_query.push_str(&format!(" AND (name ILIKE '%{}%' OR sku ILIKE '%{}%' OR description ILIKE '%{}%')", search, search, search));
        }
    }

    // Add visibility filter
    if let Some(is_visible) = params.is_visible {
        query.push_str(&format!(" AND is_visible = {}", is_visible));
        count_query.push_str(&format!(" AND is_visible = {}", is_visible));
    }

    // Add featured filter
    if let Some(is_featured) = params.is_featured {
        query.push_str(&format!(" AND is_featured = {}", is_featured));
        count_query.push_str(&format!(" AND is_featured = {}", is_featured));
    }

    // Add sync status filter
    if let Some(sync_status) = &params.sync_status {
        query.push_str(&format!(" AND sync_status = '{}'", sync_status));
        count_query.push_str(&format!(" AND sync_status = '{}'", sync_status));
    }

    // Add category filter
    if let Some(category_id) = params.category {
        query.push_str(&format!(" AND id IN (SELECT product_id FROM product_category_mappings WHERE category_id = '{}')", category_id));
        count_query.push_str(&format!(" AND id IN (SELECT product_id FROM product_category_mappings WHERE category_id = '{}')", category_id));
    }

    // Add sorting
    let sort_by = params.sort_by.as_deref().unwrap_or("created_at");
    let sort_order = params.sort_order.as_deref().unwrap_or("DESC");
    query.push_str(&format!(" ORDER BY {} {} LIMIT {} OFFSET {}", sort_by, sort_order, limit, offset));

    // Execute queries
    let products_result = sqlx::query_as::<_, Product>(&query)
        .fetch_all(&state.db_pool)
        .await;

    let count_result = sqlx::query_scalar::<_, i64>(&count_query)
        .fetch_one(&state.db_pool)
        .await;

    match (products_result, count_result) {
        (Ok(products), Ok(total)) => {
            let product_responses: Vec<ProductListResponse> = products
                .into_iter()
                .map(ProductListResponse::from)
                .collect();

            let total_pages = (total + limit - 1) / limit;

            Ok(Json(ProductListResult {
                products: product_responses,
                total,
                page,
                limit,
                total_pages,
            }))
        }
        _ => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_product(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<ProductResponse>, StatusCode> {
    // Get the main product
    let product_result = sqlx::query_as::<_, Product>(
        "SELECT * FROM products WHERE id = $1"
    )
    .bind(id)
    .fetch_one(&state.db_pool)
    .await;

    match product_result {
        Ok(product) => {
            let mut product_response = ProductResponse::from(product);

            // Get categories
            let categories = sqlx::query_as::<_, ProductCategory>(
                "SELECT pc.* FROM product_categories pc
                 JOIN product_category_mappings pcm ON pc.id = pcm.category_id
                 WHERE pcm.product_id = $1
                 ORDER BY pc.sort_order"
            )
            .bind(id)
            .fetch_all(&state.db_pool)
            .await
            .unwrap_or_default();

            product_response.categories = categories
                .into_iter()
                .map(|c| ProductCategoryResponse {
                    id: c.id,
                    name: c.name,
                    description: c.description,
                    parent_id: c.parent_id,
                    sort_order: c.sort_order,
                    is_visible: c.is_visible,
                    created_at: c.created_at,
                })
                .collect();

            // Get images
            let images = sqlx::query_as::<_, ProductImage>(
                "SELECT * FROM product_images WHERE product_id = $1 ORDER BY sort_order"
            )
            .bind(id)
            .fetch_all(&state.db_pool)
            .await
            .unwrap_or_default();

            product_response.images = images
                .into_iter()
                .map(|i| ProductImageResponse {
                    id: i.id,
                    image_url: i.image_url,
                    is_thumbnail: i.is_thumbnail,
                    sort_order: i.sort_order,
                    description: i.description,
                    created_at: i.created_at,
                })
                .collect();

            // Get videos
            let videos = sqlx::query_as::<_, ProductVideo>(
                "SELECT * FROM product_videos WHERE product_id = $1 ORDER BY sort_order"
            )
            .bind(id)
            .fetch_all(&state.db_pool)
            .await
            .unwrap_or_default();

            product_response.videos = videos
                .into_iter()
                .map(|v| ProductVideoResponse {
                    id: v.id,
                    title: v.title,
                    description: v.description,
                    sort_order: v.sort_order,
                    video_type: v.video_type,
                    video_id: v.video_id,
                    length: v.length,
                    created_at: v.created_at,
                })
                .collect();

            // Get variants with option values
            let variants = sqlx::query_as::<_, ProductVariant>(
                "SELECT * FROM product_variants WHERE product_id = $1"
            )
            .bind(id)
            .fetch_all(&state.db_pool)
            .await
            .unwrap_or_default();

            let mut variant_responses = Vec::new();
            for variant in variants {
                let option_values = sqlx::query_as::<_, VariantOptionValue>(
                    "SELECT * FROM variant_option_values WHERE variant_id = $1"
                )
                .bind(variant.id)
                .fetch_all(&state.db_pool)
                .await
                .unwrap_or_default();

                variant_responses.push(ProductVariantResponse {
                    id: variant.id,
                    sku: variant.sku,
                    cost_price: variant.cost_price,
                    price: variant.price,
                    sale_price: variant.sale_price,
                    retail_price: variant.retail_price,
                    map_price: variant.map_price,
                    weight: variant.weight,
                    width: variant.width,
                    height: variant.height,
                    depth: variant.depth,
                    is_free_shipping: variant.is_free_shipping,
                    fixed_cost_shipping_price: variant.fixed_cost_shipping_price,
                    purchasing_disabled: variant.purchasing_disabled,
                    purchasing_disabled_message: variant.purchasing_disabled_message,
                    image_url: variant.image_url,
                    upc: variant.upc,
                    inventory_level: variant.inventory_level,
                    inventory_warning_level: variant.inventory_warning_level,
                    bin_picking_number: variant.bin_picking_number,
                    mpn: variant.mpn,
                    gtin: variant.gtin,
                    calculated_price: variant.calculated_price,
                    calculated_weight: variant.calculated_weight,
                    created_at: variant.created_at,
                    option_values: option_values
                        .into_iter()
                        .map(|ov| VariantOptionValueResponse {
                            id: ov.id,
                            option_id: ov.option_id,
                            option_display_name: ov.option_display_name,
                            label: ov.label,
                            created_at: ov.created_at,
                        })
                        .collect(),
                });
            }
            product_response.variants = variant_responses;

            // Get custom fields
            let custom_fields = sqlx::query_as::<_, ProductCustomField>(
                "SELECT * FROM product_custom_fields WHERE product_id = $1"
            )
            .bind(id)
            .fetch_all(&state.db_pool)
            .await
            .unwrap_or_default();

            product_response.custom_fields = custom_fields
                .into_iter()
                .map(|cf| ProductCustomFieldResponse {
                    id: cf.id,
                    name: cf.name,
                    value: cf.value,
                    created_at: cf.created_at,
                })
                .collect();

            // Get bulk pricing rules
            let bulk_pricing_rules = sqlx::query_as::<_, BulkPricingRule>(
                "SELECT * FROM bulk_pricing_rules WHERE product_id = $1 ORDER BY quantity_min"
            )
            .bind(id)
            .fetch_all(&state.db_pool)
            .await
            .unwrap_or_default();

            product_response.bulk_pricing_rules = bulk_pricing_rules
                .into_iter()
                .map(|bpr| BulkPricingRuleResponse {
                    id: bpr.id,
                    quantity_min: bpr.quantity_min,
                    quantity_max: bpr.quantity_max,
                    rule_type: bpr.rule_type,
                    amount: bpr.amount,
                    created_at: bpr.created_at,
                })
                .collect();

            Ok(Json(product_response))
        }
        Err(sqlx::Error::RowNotFound) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn create_product(
    State(state): State<AppState>,
    Json(payload): Json<CreateProductRequest>,
) -> Result<Json<ProductResponse>, StatusCode> {
    // Validate the request
    if let Err(_) = payload.validate() {
        return Err(StatusCode::BAD_REQUEST);
    }

    // Start a transaction
    let mut tx = match state.db_pool.begin().await {
        Ok(tx) => tx,
        Err(_) => return Err(StatusCode::INTERNAL_SERVER_ERROR),
    };

    // Insert the main product
    let product_id = Uuid::new_v4();
    let product_result = sqlx::query!(
        r#"
        INSERT INTO products (
            id, name, product_type, sku, description, weight, width, depth, height,
            price, cost_price, retail_price, sale_price, map_price, tax_class_id,
            product_tax_code, brand_id, brand_name, inventory_level, inventory_warning_level,
            inventory_tracking, fixed_cost_shipping_price, is_free_shipping, is_visible,
            is_featured, warranty, bin_picking_number, layout_file, upc, search_keywords,
            availability_description, availability, gift_wrapping_options_type, sort_order,
            condition, is_condition_shown, order_quantity_minimum, order_quantity_maximum,
            page_title, meta_description, preorder_release_date, preorder_message,
            is_preorder_only, is_price_hidden, price_hidden_label, open_graph_type,
            open_graph_title, open_graph_description, open_graph_use_meta_description,
            open_graph_use_product_name, open_graph_use_image, gtin, mpn
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18,
            $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34,
            $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46, $47, $48, $49, $50, $51, $52, $53
        )
        "#,
        product_id,
        payload.name,
        payload.product_type,
        payload.sku,
        payload.description,
        payload.weight,
        payload.width,
        payload.depth,
        payload.height,
        payload.price,
        payload.cost_price,
        payload.retail_price,
        payload.sale_price,
        payload.map_price,
        payload.tax_class_id,
        payload.product_tax_code,
        payload.brand_id,
        payload.brand_name,
        payload.inventory_level,
        payload.inventory_warning_level,
        payload.inventory_tracking,
        payload.fixed_cost_shipping_price,
        payload.is_free_shipping,
        payload.is_visible,
        payload.is_featured,
        payload.warranty,
        payload.bin_picking_number,
        payload.layout_file,
        payload.upc,
        payload.search_keywords,
        payload.availability_description,
        payload.availability,
        payload.gift_wrapping_options_type,
        payload.sort_order,
        payload.condition,
        payload.is_condition_shown,
        payload.order_quantity_minimum,
        payload.order_quantity_maximum,
        payload.page_title,
        payload.meta_description,
        payload.preorder_release_date,
        payload.preorder_message,
        payload.is_preorder_only,
        payload.is_price_hidden,
        payload.price_hidden_label,
        payload.open_graph_type,
        payload.open_graph_title,
        payload.open_graph_description,
        payload.open_graph_use_meta_description,
        payload.open_graph_use_product_name,
        payload.open_graph_use_image,
        payload.gtin,
        payload.mpn
    )
    .execute(&mut *tx)
    .await;

    if product_result.is_err() {
        let _ = tx.rollback().await;
        return Err(StatusCode::INTERNAL_SERVER_ERROR);
    }

    // Insert categories
    if let Some(categories) = &payload.categories {
        for category_id in categories {
            let _ = sqlx::query!(
                "INSERT INTO product_category_mappings (product_id, category_id) VALUES ($1, $2)",
                product_id,
                category_id
            )
            .execute(&mut *tx)
            .await;
        }
    }

    // Insert images
    if let Some(images) = &payload.images {
        for image in images {
            let _ = sqlx::query!(
                "INSERT INTO product_images (product_id, image_url, is_thumbnail, sort_order, description) VALUES ($1, $2, $3, $4, $5)",
                product_id,
                image.image_url,
                image.is_thumbnail,
                image.sort_order,
                image.description
            )
            .execute(&mut *tx)
            .await;
        }
    }

    // Insert videos
    if let Some(videos) = &payload.videos {
        for video in videos {
            let _ = sqlx::query!(
                "INSERT INTO product_videos (product_id, title, description, sort_order, video_type, video_id, length) VALUES ($1, $2, $3, $4, $5, $6, $7)",
                product_id,
                video.title,
                video.description,
                video.sort_order,
                video.video_type,
                video.video_id,
                video.length
            )
            .execute(&mut *tx)
            .await;
        }
    }

    // Insert variants
    if let Some(variants) = &payload.variants {
        for variant in variants {
            let variant_id = Uuid::new_v4();
            let variant_result = sqlx::query!(
                r#"
                INSERT INTO product_variants (
                    id, product_id, sku, cost_price, price, sale_price, retail_price, map_price,
                    weight, width, height, depth, is_free_shipping, fixed_cost_shipping_price,
                    purchasing_disabled, purchasing_disabled_message, image_url, upc,
                    inventory_level, inventory_warning_level, bin_picking_number, mpn, gtin
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23
                )
                "#,
                variant_id,
                product_id,
                variant.sku,
                variant.cost_price,
                variant.price,
                variant.sale_price,
                variant.retail_price,
                variant.map_price,
                variant.weight,
                variant.width,
                variant.height,
                variant.depth,
                variant.is_free_shipping,
                variant.fixed_cost_shipping_price,
                variant.purchasing_disabled,
                variant.purchasing_disabled_message,
                variant.image_url,
                variant.upc,
                variant.inventory_level,
                variant.inventory_warning_level,
                variant.bin_picking_number,
                variant.mpn,
                variant.gtin
            )
            .execute(&mut *tx)
            .await;

            if variant_result.is_ok() {
                // Insert variant option values
                if let Some(option_values) = &variant.option_values {
                    for option_value in option_values {
                        let _ = sqlx::query!(
                            "INSERT INTO variant_option_values (variant_id, option_id, option_display_name, label) VALUES ($1, $2, $3, $4)",
                            variant_id,
                            option_value.option_id,
                            option_value.option_display_name,
                            option_value.label
                        )
                        .execute(&mut *tx)
                        .await;
                    }
                }
            }
        }
    }

    // Insert custom fields
    if let Some(custom_fields) = &payload.custom_fields {
        for custom_field in custom_fields {
            let _ = sqlx::query!(
                "INSERT INTO product_custom_fields (product_id, name, value) VALUES ($1, $2, $3)",
                product_id,
                custom_field.name,
                custom_field.value
            )
            .execute(&mut *tx)
            .await;
        }
    }

    // Insert bulk pricing rules
    if let Some(bulk_pricing_rules) = &payload.bulk_pricing_rules {
        for rule in bulk_pricing_rules {
            let _ = sqlx::query!(
                "INSERT INTO bulk_pricing_rules (product_id, quantity_min, quantity_max, rule_type, amount) VALUES ($1, $2, $3, $4, $5)",
                product_id,
                rule.quantity_min,
                rule.quantity_max,
                rule.rule_type,
                rule.amount
            )
            .execute(&mut *tx)
            .await;
        }
    }

    // Commit the transaction
    if tx.commit().await.is_err() {
        return Err(StatusCode::INTERNAL_SERVER_ERROR);
    }

    // Fetch and return the created product
    match get_product(State(state), Path(product_id)).await {
        Ok(product) => Ok(product),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn update_product(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateProductRequest>,
) -> Result<Json<ProductResponse>, StatusCode> {
    // Validate the request
    if let Err(_) = payload.validate() {
        return Err(StatusCode::BAD_REQUEST);
    }

    // Check if product exists
    let existing_product = sqlx::query_as::<_, Product>(
        "SELECT * FROM products WHERE id = $1"
    )
    .bind(id)
    .fetch_one(&state.db_pool)
    .await;

    if existing_product.is_err() {
        return Err(StatusCode::NOT_FOUND);
    }

    // Build dynamic update query
    let mut update_fields = Vec::new();
    let mut param_count = 1;

    if payload.name.is_some() {
        update_fields.push(format!("name = ${}", param_count));
        param_count += 1;
    }
    if payload.product_type.is_some() {
        update_fields.push(format!("product_type = ${}", param_count));
        param_count += 1;
    }
    if payload.sku.is_some() {
        update_fields.push(format!("sku = ${}", param_count));
        param_count += 1;
    }
    if payload.description.is_some() {
        update_fields.push(format!("description = ${}", param_count));
        param_count += 1;
    }
    if payload.price.is_some() {
        update_fields.push(format!("price = ${}", param_count));
        param_count += 1;
    }
    if payload.inventory_level.is_some() {
        update_fields.push(format!("inventory_level = ${}", param_count));
        param_count += 1;
    }
    if payload.is_visible.is_some() {
        update_fields.push(format!("is_visible = ${}", param_count));
        param_count += 1;
    }
    if payload.is_featured.is_some() {
        update_fields.push(format!("is_featured = ${}", param_count));
        param_count += 1;
    }

    if update_fields.is_empty() {
        return Err(StatusCode::BAD_REQUEST);
    }

    // Add updated_at field
    update_fields.push("updated_at = NOW()".to_string());

    let query = format!(
        "UPDATE products SET {} WHERE id = ${}",
        update_fields.join(", "),
        param_count
    );

    // Execute the update
    let mut query_builder = sqlx::query(&query);

    if let Some(name) = payload.name {
        query_builder = query_builder.bind(name);
    }
    if let Some(product_type) = payload.product_type {
        query_builder = query_builder.bind(product_type);
    }
    if let Some(sku) = payload.sku {
        query_builder = query_builder.bind(sku);
    }
    if let Some(description) = payload.description {
        query_builder = query_builder.bind(description);
    }
    if let Some(price) = payload.price {
        query_builder = query_builder.bind(price);
    }
    if let Some(inventory_level) = payload.inventory_level {
        query_builder = query_builder.bind(inventory_level);
    }
    if let Some(is_visible) = payload.is_visible {
        query_builder = query_builder.bind(is_visible);
    }
    if let Some(is_featured) = payload.is_featured {
        query_builder = query_builder.bind(is_featured);
    }

    query_builder = query_builder.bind(id);

    match query_builder.execute(&state.db_pool).await {
        Ok(_) => {
            // Fetch and return the updated product
            match get_product(State(state), Path(id)).await {
                Ok(product) => Ok(product),
                Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
            }
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn delete_product(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<Value>, StatusCode> {
    // Check if product exists
    let existing_product = sqlx::query_as::<_, Product>(
        "SELECT * FROM products WHERE id = $1"
    )
    .bind(id)
    .fetch_one(&state.db_pool)
    .await;

    if existing_product.is_err() {
        return Err(StatusCode::NOT_FOUND);
    }

    // Delete the product (cascade will handle related records)
    match sqlx::query("DELETE FROM products WHERE id = $1")
        .bind(id)
        .execute(&state.db_pool)
        .await
    {
        Ok(_) => Ok(Json(json!({
            "message": "Product deleted successfully",
            "id": id
        }))),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn list_categories(
    State(state): State<AppState>,
) -> Result<Json<Vec<ProductCategoryResponse>>, StatusCode> {
    match sqlx::query_as::<_, ProductCategory>(
        "SELECT * FROM product_categories ORDER BY sort_order, name"
    )
    .fetch_all(&state.db_pool)
    .await
    {
        Ok(categories) => {
            let category_responses: Vec<ProductCategoryResponse> = categories
                .into_iter()
                .map(|c| ProductCategoryResponse {
                    id: c.id,
                    name: c.name,
                    description: c.description,
                    parent_id: c.parent_id,
                    sort_order: c.sort_order,
                    is_visible: c.is_visible,
                    created_at: c.created_at,
                })
                .collect();

            Ok(Json(category_responses))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn sync_with_bigcommerce(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<Value>, StatusCode> {
    // Check if product exists
    let existing_product = sqlx::query_as::<_, Product>(
        "SELECT * FROM products WHERE id = $1"
    )
    .bind(id)
    .fetch_one(&state.db_pool)
    .await;

    match existing_product {
        Ok(_) => {
            // Update sync status to pending
            let _ = sqlx::query(
                "UPDATE products SET sync_status = 'pending', updated_at = NOW() WHERE id = $1"
            )
            .bind(id)
            .execute(&state.db_pool)
            .await;

            // TODO: Implement actual BigCommerce API sync
            // For now, just simulate success
            let _ = sqlx::query(
                "UPDATE products SET sync_status = 'synced', last_sync_at = NOW(), updated_at = NOW() WHERE id = $1"
            )
            .bind(id)
            .execute(&state.db_pool)
            .await;

            Ok(Json(json!({
                "message": "Product sync initiated successfully",
                "id": id,
                "status": "synced"
            })))
        }
        Err(sqlx::Error::RowNotFound) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}
