use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::J<PERSON>,
};
use serde_json::{json, Value};
use uuid::Uuid;
use validator::Validate;

use crate::{
    models::{
        CreateCustomFieldDefinitionRequest, UpdateCustomFieldDefinitionRequest,
        CreateCustomFieldValueRequest, UpdateCustomFieldValueRequest,
        CustomFieldDefinitionResponse, CustomFieldValueResponse,
        CustomFieldWebhookIntegrationResponse, CustomFieldListResponse,
        CustomFieldListQuery, CustomFieldDefinition, CustomFieldValue,
        CustomFieldWebhookIntegration,
    },
    services::AppState,
};

// List custom field definitions
pub async fn list_custom_field_definitions(
    Query(params): Query<CustomFieldListQuery>,
    State(state): State<AppState>,
) -> Result<Json<CustomFieldListResponse>, (StatusCode, Json<Value>)> {
    let page = params.page.unwrap_or(1).max(1);
    let per_page = params.per_page.unwrap_or(20).min(100);
    let offset = (page - 1) * per_page;

    // Build the query with filters
    let mut query = "SELECT * FROM custom_field_definitions WHERE 1=1".to_string();
    let mut count_query = "SELECT COUNT(*) FROM custom_field_definitions WHERE 1=1".to_string();
    let mut query_params = Vec::new();
    let mut param_count = 0;

    // Add filters
    if let Some(search) = &params.search {
        param_count += 1;
        query.push_str(&format!(" AND (label ILIKE ${} OR key ILIKE ${} OR description ILIKE ${})", param_count, param_count, param_count));
        count_query.push_str(&format!(" AND (label ILIKE ${} OR key ILIKE ${} OR description ILIKE ${})", param_count, param_count, param_count));
        query_params.push(format!("%{}%", search));
    }

    if let Some(resource_type) = &params.resource_type {
        param_count += 1;
        query.push_str(&format!(" AND resource_type = ${}", param_count));
        count_query.push_str(&format!(" AND resource_type = ${}", param_count));
        query_params.push(resource_type.clone());
    }

    if let Some(namespace) = &params.namespace {
        param_count += 1;
        query.push_str(&format!(" AND namespace = ${}", param_count));
        count_query.push_str(&format!(" AND namespace = ${}", param_count));
        query_params.push(namespace.clone());
    }

    if let Some(field_type) = &params.field_type {
        param_count += 1;
        query.push_str(&format!(" AND field_type = ${}", param_count));
        count_query.push_str(&format!(" AND field_type = ${}", param_count));
        query_params.push(field_type.clone());
    }

    if let Some(is_active) = params.is_active {
        param_count += 1;
        query.push_str(&format!(" AND is_active = ${}", param_count));
        count_query.push_str(&format!(" AND is_active = ${}", param_count));
        query_params.push(is_active.to_string());
    }

    // Add sorting
    let sort_by = params.sort_by.unwrap_or_else(|| "created_at".to_string());
    let sort_order = params.sort_order.unwrap_or_else(|| "DESC".to_string());
    query.push_str(&format!(" ORDER BY {} {} LIMIT {} OFFSET {}", sort_by, sort_order, per_page, offset));

    // Execute queries
    let mut query_builder = sqlx::query_as::<_, CustomFieldDefinition>(&query);
    let mut count_query_builder = sqlx::query_scalar::<_, i64>(&count_query);

    // Bind parameters
    for param in &query_params {
        query_builder = query_builder.bind(param);
        count_query_builder = count_query_builder.bind(param);
    }

    let fields = query_builder
        .fetch_all(&state.db_pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to fetch custom field definitions: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({"error": "Failed to fetch custom field definitions"})))
        })?;

    let total = count_query_builder
        .fetch_one(&state.db_pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to count custom field definitions: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({"error": "Failed to count custom field definitions"})))
        })?;

    // Convert to response format and fetch webhook integrations
    let mut field_responses = Vec::new();
    for field in fields {
        let webhook_integrations = sqlx::query_as::<_, CustomFieldWebhookIntegration>(
            "SELECT * FROM custom_field_webhook_integrations WHERE field_definition_id = $1"
        )
        .bind(field.id)
        .fetch_all(&state.db_pool)
        .await
        .unwrap_or_default();

        let webhook_integration_responses: Vec<CustomFieldWebhookIntegrationResponse> = webhook_integrations
            .into_iter()
            .map(|integration| CustomFieldWebhookIntegrationResponse {
                id: integration.id,
                field_definition_id: integration.field_definition_id,
                integration_name: integration.integration_name,
                webhook_url: integration.webhook_url,
                webhook_method: integration.webhook_method,
                webhook_headers: integration.webhook_headers,
                field_mapping: integration.field_mapping,
                is_active: integration.is_active,
                last_sync_at: integration.last_sync_at,
                sync_status: integration.sync_status,
                error_message: integration.error_message,
                created_at: integration.created_at,
                updated_at: integration.updated_at,
            })
            .collect();

        field_responses.push(CustomFieldDefinitionResponse {
            id: field.id,
            label: field.label,
            key: field.key,
            field_type: field.field_type,
            resource_type: field.resource_type,
            namespace: field.namespace,
            description: field.description,
            placeholder_text: field.placeholder_text,
            help_text: field.help_text,
            is_required: field.is_required,
            is_searchable: field.is_searchable,
            is_filterable: field.is_filterable,
            is_ai_enabled: field.is_ai_enabled,
            is_active: field.is_active,
            sort_order: field.sort_order,
            validation_rules: field.validation_rules,
            field_options: field.field_options,
            webhook_config: field.webhook_config,
            created_at: field.created_at,
            updated_at: field.updated_at,
            webhook_integrations: webhook_integration_responses,
        });
    }

    let total_pages = (total as f64 / per_page as f64).ceil() as i32;

    Ok(Json(CustomFieldListResponse {
        fields: field_responses,
        total,
        page,
        per_page,
        total_pages,
    }))
}

// Get custom field definition by ID
pub async fn get_custom_field_definition(
    Path(id): Path<Uuid>,
    State(state): State<AppState>,
) -> Result<Json<CustomFieldDefinitionResponse>, (StatusCode, Json<Value>)> {
    let field = sqlx::query_as::<_, CustomFieldDefinition>(
        "SELECT * FROM custom_field_definitions WHERE id = $1"
    )
    .bind(id)
    .fetch_optional(&state.db_pool)
    .await
    .map_err(|e| {
        tracing::error!("Failed to fetch custom field definition: {}", e);
        (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({"error": "Failed to fetch custom field definition"})))
    })?;

    let field = field.ok_or_else(|| {
        (StatusCode::NOT_FOUND, Json(json!({"error": "Custom field definition not found"})))
    })?;

    // Fetch webhook integrations
    let webhook_integrations = sqlx::query_as::<_, CustomFieldWebhookIntegration>(
        "SELECT * FROM custom_field_webhook_integrations WHERE field_definition_id = $1"
    )
    .bind(field.id)
    .fetch_all(&state.db_pool)
    .await
    .unwrap_or_default();

    let webhook_integration_responses: Vec<CustomFieldWebhookIntegrationResponse> = webhook_integrations
        .into_iter()
        .map(|integration| CustomFieldWebhookIntegrationResponse {
            id: integration.id,
            field_definition_id: integration.field_definition_id,
            integration_name: integration.integration_name,
            webhook_url: integration.webhook_url,
            webhook_method: integration.webhook_method,
            webhook_headers: integration.webhook_headers,
            field_mapping: integration.field_mapping,
            is_active: integration.is_active,
            last_sync_at: integration.last_sync_at,
            sync_status: integration.sync_status,
            error_message: integration.error_message,
            created_at: integration.created_at,
            updated_at: integration.updated_at,
        })
        .collect();

    Ok(Json(CustomFieldDefinitionResponse {
        id: field.id,
        label: field.label,
        key: field.key,
        field_type: field.field_type,
        resource_type: field.resource_type,
        namespace: field.namespace,
        description: field.description,
        placeholder_text: field.placeholder_text,
        help_text: field.help_text,
        is_required: field.is_required,
        is_searchable: field.is_searchable,
        is_filterable: field.is_filterable,
        is_ai_enabled: field.is_ai_enabled,
        is_active: field.is_active,
        sort_order: field.sort_order,
        validation_rules: field.validation_rules,
        field_options: field.field_options,
        webhook_config: field.webhook_config,
        created_at: field.created_at,
        updated_at: field.updated_at,
        webhook_integrations: webhook_integration_responses,
    }))
}

// Create custom field definition
pub async fn create_custom_field_definition(
    State(state): State<AppState>,
    Json(payload): Json<CreateCustomFieldDefinitionRequest>,
) -> Result<Json<CustomFieldDefinitionResponse>, (StatusCode, Json<Value>)> {
    // Validate the request
    if let Err(errors) = payload.validate() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(json!({"error": "Validation failed", "details": errors})),
        ));
    }

    // Check if key already exists for this resource type and namespace
    let existing = sqlx::query_scalar::<_, i64>(
        "SELECT COUNT(*) FROM custom_field_definitions WHERE key = $1 AND resource_type = $2 AND namespace = $3"
    )
    .bind(&payload.key)
    .bind(&payload.resource_type)
    .bind(&payload.namespace)
    .fetch_one(&state.db_pool)
    .await
    .map_err(|e| {
        tracing::error!("Failed to check existing custom field: {}", e);
        (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({"error": "Failed to check existing custom field"})))
    })?;

    if existing > 0 {
        return Err((
            StatusCode::CONFLICT,
            Json(json!({"error": "A custom field with this key already exists for this resource type and namespace"})),
        ));
    }

    let field_id = Uuid::new_v4();
    let field = sqlx::query_as::<_, CustomFieldDefinition>(
        r#"
        INSERT INTO custom_field_definitions (
            id, label, key, field_type, resource_type, namespace, description,
            placeholder_text, help_text, is_required, is_searchable, is_filterable,
            is_ai_enabled, validation_rules, field_options, webhook_config
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
        RETURNING *
        "#
    )
    .bind(field_id)
    .bind(&payload.label)
    .bind(&payload.key)
    .bind(&payload.field_type)
    .bind(&payload.resource_type)
    .bind(&payload.namespace)
    .bind(&payload.description)
    .bind(&payload.placeholder_text)
    .bind(&payload.help_text)
    .bind(payload.is_required.unwrap_or(false))
    .bind(payload.is_searchable.unwrap_or(false))
    .bind(payload.is_filterable.unwrap_or(false))
    .bind(payload.is_ai_enabled.unwrap_or(false))
    .bind(&payload.validation_rules.unwrap_or_else(|| json!({})))
    .bind(&payload.field_options.unwrap_or_else(|| json!({})))
    .bind(&payload.webhook_config.unwrap_or_else(|| json!({})))
    .fetch_one(&state.db_pool)
    .await
    .map_err(|e| {
        tracing::error!("Failed to create custom field definition: {}", e);
        (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({"error": "Failed to create custom field definition"})))
    })?;

    Ok(Json(CustomFieldDefinitionResponse {
        id: field.id,
        label: field.label,
        key: field.key,
        field_type: field.field_type,
        resource_type: field.resource_type,
        namespace: field.namespace,
        description: field.description,
        placeholder_text: field.placeholder_text,
        help_text: field.help_text,
        is_required: field.is_required,
        is_searchable: field.is_searchable,
        is_filterable: field.is_filterable,
        is_ai_enabled: field.is_ai_enabled,
        is_active: field.is_active,
        sort_order: field.sort_order,
        validation_rules: field.validation_rules,
        field_options: field.field_options,
        webhook_config: field.webhook_config,
        created_at: field.created_at,
        updated_at: field.updated_at,
        webhook_integrations: vec![],
    }))
}

// Delete custom field definition
pub async fn delete_custom_field_definition(
    Path(id): Path<Uuid>,
    State(state): State<AppState>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    let result = sqlx::query!(
        "DELETE FROM custom_field_definitions WHERE id = $1",
        id
    )
    .execute(&state.db_pool)
    .await
    .map_err(|e| {
        tracing::error!("Failed to delete custom field definition: {}", e);
        (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({"error": "Failed to delete custom field definition"})))
    })?;

    if result.rows_affected() == 0 {
        return Err((
            StatusCode::NOT_FOUND,
            Json(json!({"error": "Custom field definition not found"})),
        ));
    }

    Ok(Json(json!({"message": "Custom field definition deleted successfully"})))
}

// Update custom field definition
pub async fn update_custom_field_definition(
    Path(id): Path<Uuid>,
    State(state): State<AppState>,
    Json(payload): Json<UpdateCustomFieldDefinitionRequest>,
) -> Result<Json<CustomFieldDefinitionResponse>, (StatusCode, Json<Value>)> {
    // Validate the request
    if let Err(errors) = payload.validate() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(json!({"error": "Validation failed", "details": errors})),
        ));
    }

    // Build dynamic update query
    let mut set_clauses = Vec::new();
    let mut params: Vec<String> = Vec::new();
    let mut param_count = 0;

    if let Some(label) = &payload.label {
        param_count += 1;
        set_clauses.push(format!("label = ${}", param_count));
        params.push(label.clone());
    }

    if let Some(description) = &payload.description {
        param_count += 1;
        set_clauses.push(format!("description = ${}", param_count));
        params.push(description.clone());
    }

    if let Some(placeholder_text) = &payload.placeholder_text {
        param_count += 1;
        set_clauses.push(format!("placeholder_text = ${}", param_count));
        params.push(placeholder_text.clone());
    }

    if let Some(help_text) = &payload.help_text {
        param_count += 1;
        set_clauses.push(format!("help_text = ${}", param_count));
        params.push(help_text.clone());
    }

    if let Some(is_required) = payload.is_required {
        param_count += 1;
        set_clauses.push(format!("is_required = ${}", param_count));
        params.push(is_required.to_string());
    }

    if let Some(is_searchable) = payload.is_searchable {
        param_count += 1;
        set_clauses.push(format!("is_searchable = ${}", param_count));
        params.push(is_searchable.to_string());
    }

    if let Some(is_filterable) = payload.is_filterable {
        param_count += 1;
        set_clauses.push(format!("is_filterable = ${}", param_count));
        params.push(is_filterable.to_string());
    }

    if let Some(is_ai_enabled) = payload.is_ai_enabled {
        param_count += 1;
        set_clauses.push(format!("is_ai_enabled = ${}", param_count));
        params.push(is_ai_enabled.to_string());
    }

    if let Some(is_active) = payload.is_active {
        param_count += 1;
        set_clauses.push(format!("is_active = ${}", param_count));
        params.push(is_active.to_string());
    }

    if let Some(sort_order) = payload.sort_order {
        param_count += 1;
        set_clauses.push(format!("sort_order = ${}", param_count));
        params.push(sort_order.to_string());
    }

    if set_clauses.is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(json!({"error": "No fields to update"})),
        ));
    }

    param_count += 1;
    let query = format!(
        "UPDATE custom_field_definitions SET {} WHERE id = ${} RETURNING *",
        set_clauses.join(", "),
        param_count
    );

    let mut query_builder = sqlx::query_as::<_, CustomFieldDefinition>(&query);
    for param in params {
        query_builder = query_builder.bind(param);
    }
    query_builder = query_builder.bind(id);

    let field = query_builder
        .fetch_optional(&state.db_pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to update custom field definition: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({"error": "Failed to update custom field definition"})))
        })?;

    let field = field.ok_or_else(|| {
        (StatusCode::NOT_FOUND, Json(json!({"error": "Custom field definition not found"})))
    })?;

    // Fetch webhook integrations
    let webhook_integrations = sqlx::query_as::<_, CustomFieldWebhookIntegration>(
        "SELECT * FROM custom_field_webhook_integrations WHERE field_definition_id = $1"
    )
    .bind(field.id)
    .fetch_all(&state.db_pool)
    .await
    .unwrap_or_default();

    let webhook_integration_responses: Vec<CustomFieldWebhookIntegrationResponse> = webhook_integrations
        .into_iter()
        .map(|integration| CustomFieldWebhookIntegrationResponse {
            id: integration.id,
            field_definition_id: integration.field_definition_id,
            integration_name: integration.integration_name,
            webhook_url: integration.webhook_url,
            webhook_method: integration.webhook_method,
            webhook_headers: integration.webhook_headers,
            field_mapping: integration.field_mapping,
            is_active: integration.is_active,
            last_sync_at: integration.last_sync_at,
            sync_status: integration.sync_status,
            error_message: integration.error_message,
            created_at: integration.created_at,
            updated_at: integration.updated_at,
        })
        .collect();

    Ok(Json(CustomFieldDefinitionResponse {
        id: field.id,
        label: field.label,
        key: field.key,
        field_type: field.field_type,
        resource_type: field.resource_type,
        namespace: field.namespace,
        description: field.description,
        placeholder_text: field.placeholder_text,
        help_text: field.help_text,
        is_required: field.is_required,
        is_searchable: field.is_searchable,
        is_filterable: field.is_filterable,
        is_ai_enabled: field.is_ai_enabled,
        is_active: field.is_active,
        sort_order: field.sort_order,
        validation_rules: field.validation_rules,
        field_options: field.field_options,
        webhook_config: field.webhook_config,
        created_at: field.created_at,
        updated_at: field.updated_at,
        webhook_integrations: webhook_integration_responses,
    }))
}
