const express = require('express');
const router = express.Router();
const WhatsAppService = require('../services/whatsappService');
const SeleniumService = require('../services/seleniumService');
const { authenticateToken } = require('../middleware/auth');

// Initialize services
const whatsappService = new WhatsAppService();
const seleniumService = new SeleniumService();

// Webhook endpoint for Chrome extension
router.post('/webhook', async (req, res) => {
  try {
    const { type, data, accountInfo, timestamp } = req.body;
    
    console.log('WhatsApp webhook received:', { type, timestamp });
    
    switch (type) {
      case 'message':
        await whatsappService.processMessage(data, accountInfo);
        break;
        
      case 'conversation':
        await whatsappService.processConversation(data, accountInfo);
        break;
        
      case 'status_update':
        await whatsappService.updateAccountStatus(data, accountInfo);
        break;
        
      case 'typing':
        await whatsappService.processTypingIndicator(data, accountInfo);
        break;
        
      default:
        console.log('Unknown webhook type:', type);
    }
    
    res.json({ success: true, message: 'Webhook processed successfully' });
  } catch (error) {
    console.error('Error processing webhook:', error);
    res.status(500).json({ error: 'Failed to process webhook' });
  }
});

// Get all WhatsApp accounts
router.get('/accounts', authenticateToken, async (req, res) => {
  try {
    const accounts = await whatsappService.getAccounts(req.user.organizationId);
    res.json(accounts);
  } catch (error) {
    console.error('Error getting accounts:', error);
    res.status(500).json({ error: 'Failed to get accounts' });
  }
});

// Create new WhatsApp account
router.post('/accounts', authenticateToken, async (req, res) => {
  try {
    const { name, phone } = req.body;
    
    if (!name || !phone) {
      return res.status(400).json({ error: 'Name and phone are required' });
    }
    
    const account = await whatsappService.createAccount({
      name,
      phone,
      organizationId: req.user.organizationId,
      createdBy: req.user.id
    });
    
    res.status(201).json(account);
  } catch (error) {
    console.error('Error creating account:', error);
    res.status(500).json({ error: 'Failed to create account' });
  }
});

// Get account details
router.get('/accounts/:accountId', authenticateToken, async (req, res) => {
  try {
    const account = await whatsappService.getAccount(req.params.accountId, req.user.organizationId);
    
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }
    
    res.json(account);
  } catch (error) {
    console.error('Error getting account:', error);
    res.status(500).json({ error: 'Failed to get account' });
  }
});

// Update account
router.put('/accounts/:accountId', authenticateToken, async (req, res) => {
  try {
    const account = await whatsappService.updateAccount(
      req.params.accountId,
      req.body,
      req.user.organizationId
    );
    
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }
    
    res.json(account);
  } catch (error) {
    console.error('Error updating account:', error);
    res.status(500).json({ error: 'Failed to update account' });
  }
});

// Delete account
router.delete('/accounts/:accountId', authenticateToken, async (req, res) => {
  try {
    await whatsappService.deleteAccount(req.params.accountId, req.user.organizationId);
    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting account:', error);
    res.status(500).json({ error: 'Failed to delete account' });
  }
});

// Get conversations for an account
router.get('/accounts/:accountId/conversations', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, search } = req.query;
    
    const conversations = await whatsappService.getConversations(
      req.params.accountId,
      req.user.organizationId,
      { page: parseInt(page), limit: parseInt(limit), search }
    );
    
    res.json(conversations);
  } catch (error) {
    console.error('Error getting conversations:', error);
    res.status(500).json({ error: 'Failed to get conversations' });
  }
});

// Get messages for a conversation
router.get('/conversations/:conversationId/messages', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 50 } = req.query;
    
    const messages = await whatsappService.getMessages(
      req.params.conversationId,
      req.user.organizationId,
      { page: parseInt(page), limit: parseInt(limit) }
    );
    
    res.json(messages);
  } catch (error) {
    console.error('Error getting messages:', error);
    res.status(500).json({ error: 'Failed to get messages' });
  }
});

// Send message
router.post('/accounts/:accountId/send-message', authenticateToken, async (req, res) => {
  try {
    const { contactPhone, message, messageType = 'text' } = req.body;
    
    if (!contactPhone || !message) {
      return res.status(400).json({ error: 'Contact phone and message are required' });
    }
    
    // Send message via Selenium
    const result = await seleniumService.sendMessage(
      req.params.accountId,
      contactPhone,
      message,
      messageType
    );
    
    if (result.success) {
      // Store message in database
      await whatsappService.storeOutgoingMessage({
        accountId: req.params.accountId,
        contactPhone,
        message,
        messageType,
        organizationId: req.user.organizationId
      });
      
      res.json({ success: true, messageId: result.messageId });
    } else {
      res.status(500).json({ error: result.error });
    }
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
});

// Start Selenium session for account
router.post('/accounts/:accountId/start-session', authenticateToken, async (req, res) => {
  try {
    const result = await seleniumService.startSession(req.params.accountId);
    
    if (result.success) {
      await whatsappService.updateAccountStatus(req.params.accountId, 'connecting');
      res.json({ success: true, sessionId: result.sessionId });
    } else {
      res.status(500).json({ error: result.error });
    }
  } catch (error) {
    console.error('Error starting session:', error);
    res.status(500).json({ error: 'Failed to start session' });
  }
});

// Stop Selenium session for account
router.post('/accounts/:accountId/stop-session', authenticateToken, async (req, res) => {
  try {
    const result = await seleniumService.stopSession(req.params.accountId);
    
    await whatsappService.updateAccountStatus(req.params.accountId, 'disconnected');
    res.json({ success: true });
  } catch (error) {
    console.error('Error stopping session:', error);
    res.status(500).json({ error: 'Failed to stop session' });
  }
});

// Get QR code for account setup
router.get('/accounts/:accountId/qr-code', authenticateToken, async (req, res) => {
  try {
    const qrCode = await seleniumService.getQRCode(req.params.accountId);
    
    if (qrCode) {
      res.json({ qrCode });
    } else {
      res.status(404).json({ error: 'QR code not available' });
    }
  } catch (error) {
    console.error('Error getting QR code:', error);
    res.status(500).json({ error: 'Failed to get QR code' });
  }
});

// Get contacts for an account
router.get('/accounts/:accountId/contacts', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 50, search } = req.query;
    
    const contacts = await whatsappService.getContacts(
      req.params.accountId,
      req.user.organizationId,
      { page: parseInt(page), limit: parseInt(limit), search }
    );
    
    res.json(contacts);
  } catch (error) {
    console.error('Error getting contacts:', error);
    res.status(500).json({ error: 'Failed to get contacts' });
  }
});

// Create or update contact
router.post('/accounts/:accountId/contacts', authenticateToken, async (req, res) => {
  try {
    const contact = await whatsappService.createOrUpdateContact(
      req.params.accountId,
      req.body,
      req.user.organizationId
    );
    
    res.json(contact);
  } catch (error) {
    console.error('Error creating/updating contact:', error);
    res.status(500).json({ error: 'Failed to create/update contact' });
  }
});

// Get analytics for an account
router.get('/accounts/:accountId/analytics', authenticateToken, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const analytics = await whatsappService.getAnalytics(
      req.params.accountId,
      req.user.organizationId,
      { startDate, endDate }
    );
    
    res.json(analytics);
  } catch (error) {
    console.error('Error getting analytics:', error);
    res.status(500).json({ error: 'Failed to get analytics' });
  }
});

// Bulk send messages
router.post('/accounts/:accountId/bulk-send', authenticateToken, async (req, res) => {
  try {
    const { contacts, message, messageType = 'text' } = req.body;
    
    if (!contacts || !Array.isArray(contacts) || !message) {
      return res.status(400).json({ error: 'Contacts array and message are required' });
    }
    
    const results = await seleniumService.bulkSendMessages(
      req.params.accountId,
      contacts,
      message,
      messageType
    );
    
    res.json(results);
  } catch (error) {
    console.error('Error bulk sending messages:', error);
    res.status(500).json({ error: 'Failed to bulk send messages' });
  }
});

// Get message templates
router.get('/accounts/:accountId/templates', authenticateToken, async (req, res) => {
  try {
    const templates = await whatsappService.getMessageTemplates(
      req.params.accountId,
      req.user.organizationId
    );
    
    res.json(templates);
  } catch (error) {
    console.error('Error getting templates:', error);
    res.status(500).json({ error: 'Failed to get templates' });
  }
});

// Create message template
router.post('/accounts/:accountId/templates', authenticateToken, async (req, res) => {
  try {
    const template = await whatsappService.createMessageTemplate(
      req.params.accountId,
      req.body,
      req.user.organizationId,
      req.user.id
    );
    
    res.status(201).json(template);
  } catch (error) {
    console.error('Error creating template:', error);
    res.status(500).json({ error: 'Failed to create template' });
  }
});

module.exports = router;
