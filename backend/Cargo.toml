[package]
name = "nucleux-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "fs"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database - PostgreSQL
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"] }

# Database - MongoDB
mongodb = "2.8"

# Redis
redis = { version = "0.24", features = ["tokio-comp"] }

# Environment and configuration
dotenv = "0.15"
config = "0.14"

# Date and time
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.0", features = ["v4", "serde"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# HTTP client
reqwest = { version = "0.11", features = ["json"] }

# Validation
validator = { version = "0.18", features = ["derive"] }

# Password hashing
bcrypt = "0.15"

# JWT
jsonwebtoken = "9.2"

# Async traits
async-trait = "0.1"
