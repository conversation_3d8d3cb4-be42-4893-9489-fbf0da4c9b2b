#!/bin/bash

echo "🚀 Setting up Nucleux Development Environment"
echo "============================================="

# Check prerequisites
echo "📋 Checking prerequisites..."

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    echo "❌ Rust is not installed. Please install Rust from https://rustup.rs/"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker from https://docker.com/"
    exit 1
fi

echo "✅ All prerequisites are installed"

# Setup backend
echo ""
echo "🦀 Setting up Rust backend..."
cd backend

# Copy environment file
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
fi

# Install dependencies and check compilation
echo "📦 Installing Rust dependencies..."
cargo check
if [ $? -eq 0 ]; then
    echo "✅ Rust backend setup complete"
else
    echo "❌ Rust backend setup failed"
    exit 1
fi

cd ..

# Setup frontend
echo ""
echo "⚛️  Setting up React frontend..."
cd frontend

# Copy environment file
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
fi

# Install dependencies
echo "📦 Installing Node.js dependencies..."
npm install
if [ $? -eq 0 ]; then
    echo "✅ React frontend setup complete"
else
    echo "❌ React frontend setup failed"
    exit 1
fi

cd ..

# Setup databases
echo ""
echo "🗄️  Setting up databases..."
echo "Starting Docker containers..."
docker-compose up -d

if [ $? -eq 0 ]; then
    echo "✅ Database containers started successfully"
    echo ""
    echo "🎉 Setup Complete!"
    echo "=================="
    echo ""
    echo "Next steps:"
    echo "1. Backend: cd backend && cargo run"
    echo "2. Frontend: cd frontend && npm run dev"
    echo "3. Visit http://localhost:5173 to see your app"
    echo ""
    echo "Database connections:"
    echo "- PostgreSQL: localhost:5432"
    echo "- MongoDB: localhost:27017"
    echo "- Redis: localhost:6379"
else
    echo "❌ Failed to start database containers"
    exit 1
fi
