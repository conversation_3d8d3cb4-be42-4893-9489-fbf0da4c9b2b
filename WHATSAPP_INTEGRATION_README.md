# WhatsApp Business Integration

A comprehensive WhatsApp Business integration for your admin dashboard that allows you to manage multiple WhatsApp accounts, send/receive messages, and automate conversations using Chrome Extension + Selenium automation.

## 🎯 **Features**

### ✅ **Multi-Account Management**
- Connect multiple WhatsApp Business accounts
- Real-time status monitoring
- Account switching and management
- Session persistence

### ✅ **Real-time Messaging**
- Live message synchronization via Chrome Extension
- Send/receive messages through the dashboard
- Message status tracking (sent, delivered, read)
- Typing indicators and online status

### ✅ **Conversation Management**
- Organized conversation list
- Search and filter conversations
- Unread message counts
- Pinned conversations
- Message history

### ✅ **Automation Features**
- Selenium-based message sending
- Bulk message broadcasting
- Message templates
- Auto-responses (planned)
- Scheduled messages (planned)

### ✅ **Analytics & Reporting**
- Message statistics
- Response time tracking
- Contact engagement metrics
- Daily/weekly/monthly reports

## 🏗️ **Architecture**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   WhatsApp Web  │◄──►│ Chrome Extension │◄──►│  Backend API    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Real-time      │    │   PostgreSQL    │
                       │   Dashboard      │    │   Database      │
                       └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Selenium       │    │   File Storage  │
                       │   Automation     │    │   (Media)       │
                       └──────────────────┘    └─────────────────┘
```

## 🚀 **Setup Instructions**

### **1. Database Setup**

```bash
# Run the database migration
psql -d nucleux_db -f database/migrations/create_whatsapp_tables.sql
```

### **2. Backend Setup**

```bash
# Install dependencies
cd backend
npm install selenium-webdriver

# Add environment variables
echo "WHATSAPP_SESSION_DIR=./sessions" >> .env
echo "SELENIUM_CHROME_PATH=/usr/bin/google-chrome" >> .env
```

### **3. Chrome Extension Installation**

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked" and select the `chrome-extension` folder
4. The extension will appear in your browser toolbar

### **4. Frontend Integration**

The WhatsApp interface is already integrated into your admin dashboard at `/apps/whatsapp`.

## 📱 **Usage Guide**

### **Setting Up a WhatsApp Account**

1. **Navigate to WhatsApp Page**
   ```
   http://localhost:5173/apps/whatsapp
   ```

2. **Add New Account**
   - Click "Add Account" button
   - Enter account name and phone number
   - Start Selenium session

3. **QR Code Authentication**
   - Open WhatsApp Web session
   - Scan QR code with your phone
   - Wait for connection confirmation

4. **Chrome Extension Setup**
   - Install the Chrome extension
   - Navigate to `web.whatsapp.com`
   - The extension will automatically start capturing data

### **Sending Messages**

#### **Via Dashboard**
```javascript
// API endpoint
POST /api/whatsapp/accounts/:accountId/send-message

// Payload
{
  "contactPhone": "+**********",
  "message": "Hello from the dashboard!",
  "messageType": "text"
}
```

#### **Via Chrome Extension**
- Use the extension popup to send quick messages
- Messages are automatically synced to the dashboard

### **Bulk Messaging**
```javascript
POST /api/whatsapp/accounts/:accountId/bulk-send

{
  "contacts": [
    { "phone": "+**********", "name": "John" },
    { "phone": "+**********", "name": "Jane" }
  ],
  "message": "Bulk message content",
  "messageType": "text"
}
```

## 🔧 **API Endpoints**

### **Account Management**
- `GET /api/whatsapp/accounts` - List all accounts
- `POST /api/whatsapp/accounts` - Create new account
- `GET /api/whatsapp/accounts/:id` - Get account details
- `PUT /api/whatsapp/accounts/:id` - Update account
- `DELETE /api/whatsapp/accounts/:id` - Delete account

### **Messaging**
- `GET /api/whatsapp/accounts/:id/conversations` - Get conversations
- `GET /api/whatsapp/conversations/:id/messages` - Get messages
- `POST /api/whatsapp/accounts/:id/send-message` - Send message
- `POST /api/whatsapp/accounts/:id/bulk-send` - Bulk send

### **Session Management**
- `POST /api/whatsapp/accounts/:id/start-session` - Start Selenium session
- `POST /api/whatsapp/accounts/:id/stop-session` - Stop Selenium session
- `GET /api/whatsapp/accounts/:id/qr-code` - Get QR code

### **Analytics**
- `GET /api/whatsapp/accounts/:id/analytics` - Get analytics data
- `GET /api/whatsapp/accounts/:id/contacts` - Get contacts

## 🗄️ **Database Schema**

### **Key Tables**
- `whatsapp_accounts` - Account information and settings
- `whatsapp_contacts` - Contact management
- `whatsapp_conversations` - Conversation threads
- `whatsapp_messages` - Message storage
- `whatsapp_message_templates` - Reusable templates
- `whatsapp_analytics` - Usage statistics

## 🔒 **Security Considerations**

### **Session Security**
- Sessions are stored locally per account
- Automatic session cleanup on disconnect
- Encrypted session data storage

### **API Security**
- JWT authentication required
- Organization-level data isolation
- Rate limiting on message endpoints

### **Chrome Extension Security**
- Content Security Policy implemented
- Secure communication with backend
- No sensitive data stored in extension

## 🚨 **Important Notes**

### **WhatsApp Terms of Service**
- This integration is for business use only
- Ensure compliance with WhatsApp Business API terms
- Avoid spam and respect user privacy
- Consider using official WhatsApp Business API for production

### **Rate Limiting**
- WhatsApp Web has built-in rate limits
- Implement delays between bulk messages
- Monitor for account restrictions

### **Reliability**
- WhatsApp Web UI can change, affecting automation
- Regular testing and updates required
- Fallback mechanisms recommended

## 🔧 **Troubleshooting**

### **Common Issues**

1. **Chrome Extension Not Working**
   ```bash
   # Check if extension is loaded
   # Verify permissions in manifest.json
   # Check console for errors
   ```

2. **Selenium Session Fails**
   ```bash
   # Verify Chrome/ChromeDriver compatibility
   # Check session directory permissions
   # Ensure WhatsApp Web is accessible
   ```

3. **Messages Not Syncing**
   ```bash
   # Check Chrome extension connection
   # Verify webhook endpoint is reachable
   # Check database connectivity
   ```

### **Debug Mode**
```bash
# Enable debug logging
export DEBUG=whatsapp:*
npm start
```

## 📈 **Performance Optimization**

### **Database Optimization**
- Indexed queries for fast message retrieval
- Partitioned tables for large datasets
- Regular cleanup of old data

### **Selenium Optimization**
- Headless mode for production
- Session reuse and pooling
- Resource cleanup and monitoring

### **Real-time Updates**
- WebSocket connections for live updates
- Efficient data synchronization
- Minimal DOM manipulation

## 🔮 **Future Enhancements**

- [ ] WhatsApp Business API integration
- [ ] Advanced automation rules
- [ ] AI-powered auto-responses
- [ ] Voice message support
- [ ] Group chat management
- [ ] Advanced analytics dashboard
- [ ] Mobile app integration
- [ ] Multi-language support

## 📞 **Support**

For technical support or questions about the WhatsApp integration:

1. Check the troubleshooting section above
2. Review the API documentation
3. Check Chrome extension console logs
4. Verify database connectivity and schema

---

**⚠️ Disclaimer**: This integration uses WhatsApp Web automation and should be used responsibly in compliance with WhatsApp's terms of service. For production use, consider the official WhatsApp Business API.
