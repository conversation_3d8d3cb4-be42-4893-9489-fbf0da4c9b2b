<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nucleux Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const { useState } = React;
        
        function TestApp() {
            const [count, setCount] = useState(0);
            
            return (
                <div className="min-h-screen bg-blue-500 p-8">
                    <h1 className="text-4xl font-bold text-white mb-4">
                        🎉 NUCLEUX TEST PAGE 🎉
                    </h1>
                    <p className="text-2xl text-white mb-4">
                        If you can see this, React is working!
                    </p>
                    <div className="bg-white p-6 rounded-lg shadow-lg mb-4">
                        <h2 className="text-xl font-bold text-gray-800 mb-2">
                            Interactive Test
                        </h2>
                        <p className="text-gray-600 mb-4">
                            Click count: {count}
                        </p>
                        <button 
                            onClick={() => setCount(count + 1)}
                            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                        >
                            Click Me!
                        </button>
                    </div>
                    <div className="bg-green-100 p-4 rounded border border-green-300">
                        <h3 className="font-bold text-green-800">Next Steps:</h3>
                        <ol className="list-decimal list-inside text-green-700 mt-2">
                            <li>If this page works, React is functional</li>
                            <li>Open terminal in your Nucleux/frontend folder</li>
                            <li>Run: npm install</li>
                            <li>Run: npm run dev</li>
                            <li>Open: http://localhost:5173</li>
                        </ol>
                    </div>
                </div>
            );
        }
        
        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>
</body>
</html>
