// Permission checking utilities and RBAC functions
import { Permission, Role, UserContext, UserMembership } from '../types/auth'
import { RESOURCES, ACTIONS, SCOPES } from '../types/auth'

// Permission checking utility
export class PermissionChecker {
  private permissions: Permission[]
  private role: Role | null
  private memberships: UserMembership[]
  private currentWorkspaceId: string | null
  private currentOrganizationId: string | null

  constructor(
    permissions: Permission[],
    role: Role | null,
    memberships: UserMembership[],
    currentWorkspaceId: string | null = null,
    currentOrganizationId: string | null = null
  ) {
    this.permissions = permissions
    this.role = role
    this.memberships = memberships
    this.currentWorkspaceId = currentWorkspaceId
    this.currentOrganizationId = currentOrganizationId
  }

  // Check if user has a specific permission
  hasPermission(resource: string, action: string, scope?: string, targetResourceId?: string): boolean {
    // Super admin has all permissions
    if (this.role?.id === 'super_admin') {
      return true
    }

    // Check direct permissions
    const hasDirectPermission = this.permissions.some(permission => {
      const resourceMatch = permission.resource === resource
      const actionMatch = permission.action === action
      const scopeMatch = !scope || permission.scope === scope

      if (!resourceMatch || !actionMatch || !scopeMatch) {
        return false
      }

      // Check scope-specific conditions
      return this.checkScopeConditions(permission, targetResourceId)
    })

    if (hasDirectPermission) {
      return true
    }

    // Check role-based permissions
    if (this.role) {
      return this.role.permissions.some(permission => {
        const resourceMatch = permission.resource === resource
        const actionMatch = permission.action === action
        const scopeMatch = !scope || permission.scope === scope

        if (!resourceMatch || !actionMatch || !scopeMatch) {
          return false
        }

        return this.checkScopeConditions(permission, targetResourceId)
      })
    }

    return false
  }

  // Check scope-specific conditions
  private checkScopeConditions(permission: Permission, _targetResourceId?: string): boolean {
    switch (permission.scope) {
      case SCOPES.GLOBAL:
        return true

      case SCOPES.ORGANIZATION:
        return !!this.currentOrganizationId

      case SCOPES.WORKSPACE:
        return !!this.currentWorkspaceId

      case SCOPES.OWN:
        // For 'own' scope, we need to verify ownership
        // This would typically involve checking if the target resource belongs to the user
        return true // Simplified for now

      default:
        return false
    }
  }

  // Check if user has any of the specified permissions
  hasAnyPermission(permissionChecks: Array<{ resource: string; action: string; scope?: string }>): boolean {
    return permissionChecks.some(check =>
      this.hasPermission(check.resource, check.action, check.scope)
    )
  }

  // Check if user has all of the specified permissions
  hasAllPermissions(permissionChecks: Array<{ resource: string; action: string; scope?: string }>): boolean {
    return permissionChecks.every(check =>
      this.hasPermission(check.resource, check.action, check.scope)
    )
  }

  // Check if user has a specific role
  hasRole(roleId: string): boolean {
    return this.role?.id === roleId
  }

  // Check if user has any of the specified roles
  hasAnyRole(roleIds: string[]): boolean {
    return roleIds.some(roleId => this.hasRole(roleId))
  }

  // Check if user can access a specific workspace
  canAccessWorkspace(workspaceId: string): boolean {
    return this.memberships.some(
      membership => membership.workspaceId === workspaceId && membership.status === 'active'
    )
  }

  // Check if user can access a specific organization
  canAccessOrganization(organizationId: string): boolean {
    return this.memberships.some(
      membership => membership.organizationId === organizationId && membership.status === 'active'
    )
  }

  // Get user's role in a specific workspace
  getRoleInWorkspace(workspaceId: string): string | null {
    const membership = this.memberships.find(
      m => m.workspaceId === workspaceId && m.status === 'active'
    )
    return membership?.roleId || null
  }

  // Check time-based permissions
  hasTimeBasedAccess(permission: Permission): boolean {
    if (!permission.conditions?.timeRestriction) {
      return true
    }

    const { startTime, endTime } = permission.conditions.timeRestriction
    const now = new Date()

    // This is a simplified check - in a real app you'd use a proper timezone library
    const currentHour = now.getHours()
    const startHour = parseInt(startTime.split(':')[0])
    const endHour = parseInt(endTime.split(':')[0])

    return currentHour >= startHour && currentHour <= endHour
  }

  // Check IP-based restrictions
  hasIPAccess(permission: Permission, userIP: string): boolean {
    if (!permission.conditions?.ipRestriction) {
      return true
    }

    return permission.conditions.ipRestriction.includes(userIP)
  }
}

// Predefined permission sets for common operations
export const PERMISSION_SETS = {
  // Integration permissions
  INTEGRATION_FULL: [
    { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.CREATE },
    { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.READ },
    { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.UPDATE },
    { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.DELETE },
    { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.CONFIGURE }
  ],

  INTEGRATION_MANAGE: [
    { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.READ },
    { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.UPDATE },
    { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.CONFIGURE }
  ],

  INTEGRATION_VIEW: [
    { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.READ }
  ],

  // Job permissions
  JOB_FULL: [
    { resource: RESOURCES.JOBS, action: ACTIONS.CREATE },
    { resource: RESOURCES.JOBS, action: ACTIONS.READ },
    { resource: RESOURCES.JOBS, action: ACTIONS.UPDATE },
    { resource: RESOURCES.JOBS, action: ACTIONS.DELETE },
    { resource: RESOURCES.JOBS, action: ACTIONS.EXECUTE }
  ],

  JOB_MANAGE: [
    { resource: RESOURCES.JOBS, action: ACTIONS.READ },
    { resource: RESOURCES.JOBS, action: ACTIONS.UPDATE },
    { resource: RESOURCES.JOBS, action: ACTIONS.EXECUTE }
  ],

  JOB_VIEW: [
    { resource: RESOURCES.JOBS, action: ACTIONS.READ }
  ],

  // User management permissions
  USER_FULL: [
    { resource: RESOURCES.USERS, action: ACTIONS.READ },
    { resource: RESOURCES.USERS, action: ACTIONS.INVITE },
    { resource: RESOURCES.USERS, action: ACTIONS.UPDATE },
    { resource: RESOURCES.USERS, action: ACTIONS.DELETE }
  ],

  USER_INVITE: [
    { resource: RESOURCES.USERS, action: ACTIONS.READ },
    { resource: RESOURCES.USERS, action: ACTIONS.INVITE }
  ],

  USER_VIEW: [
    { resource: RESOURCES.USERS, action: ACTIONS.READ }
  ],

  // Data permissions
  DATA_FULL: [
    { resource: RESOURCES.DATA, action: ACTIONS.READ },
    { resource: RESOURCES.DATA, action: ACTIONS.EXPORT },
    { resource: RESOURCES.DATA, action: ACTIONS.IMPORT }
  ],

  DATA_VIEW: [
    { resource: RESOURCES.DATA, action: ACTIONS.READ }
  ],

  // Settings permissions
  SETTINGS_FULL: [
    { resource: RESOURCES.SETTINGS, action: ACTIONS.READ },
    { resource: RESOURCES.SETTINGS, action: ACTIONS.UPDATE }
  ],

  SETTINGS_VIEW: [
    { resource: RESOURCES.SETTINGS, action: ACTIONS.READ }
  ],

  // Billing permissions
  BILLING_FULL: [
    { resource: RESOURCES.BILLING, action: ACTIONS.READ },
    { resource: RESOURCES.BILLING, action: ACTIONS.UPDATE }
  ],

  BILLING_VIEW: [
    { resource: RESOURCES.BILLING, action: ACTIONS.READ }
  ]
} as const

// Helper functions for common permission checks
export const canManageIntegrations = (checker: PermissionChecker): boolean => {
  return checker.hasAnyPermission([...PERMISSION_SETS.INTEGRATION_MANAGE])
}

export const canCreateJobs = (checker: PermissionChecker): boolean => {
  return checker.hasPermission(RESOURCES.JOBS, ACTIONS.CREATE)
}

export const canInviteUsers = (checker: PermissionChecker): boolean => {
  return checker.hasPermission(RESOURCES.USERS, ACTIONS.INVITE)
}

export const canViewBilling = (checker: PermissionChecker): boolean => {
  return checker.hasPermission(RESOURCES.BILLING, ACTIONS.READ)
}

export const canExportData = (checker: PermissionChecker): boolean => {
  return checker.hasPermission(RESOURCES.DATA, ACTIONS.EXPORT)
}

export const canManageSettings = (checker: PermissionChecker): boolean => {
  return checker.hasPermission(RESOURCES.SETTINGS, ACTIONS.UPDATE)
}

// Role hierarchy checking
export const isAdmin = (checker: PermissionChecker): boolean => {
  return checker.hasAnyRole(['super_admin', 'org_owner', 'org_admin', 'workspace_owner', 'workspace_admin'])
}

export const isManager = (checker: PermissionChecker): boolean => {
  return checker.hasAnyRole(['integration_manager', 'workspace_owner', 'workspace_admin'])
}

export const isDeveloper = (checker: PermissionChecker): boolean => {
  return checker.hasRole('developer')
}

export const isViewer = (checker: PermissionChecker): boolean => {
  return checker.hasRole('viewer')
}

// Create permission checker from user context
export const createPermissionChecker = (userContext: UserContext | null): PermissionChecker | null => {
  if (!userContext) return null

  return new PermissionChecker(
    userContext.permissions,
    userContext.currentRole,
    userContext.memberships,
    userContext.currentWorkspace?.id,
    userContext.currentOrganization?.id
  )
}

export default PermissionChecker
