// Mock API services to simulate real API calls
import {
  mockApps,
  mockJobs,
  mockProducts,
  mockOrders,
  mockCustomers,
  mockJobLogs,
  App,
  Job,
  Product,
  Order,
  Customer,
  JobLog
} from '../data/mockData'

// Simulate network delay
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// Mock API responses
export const mockApiService = {
  // Apps API
  apps: {
    getAll: async (): Promise<App[]> => {
      await delay()
      return [...mockApps]
    },

    getById: async (id: string): Promise<App | null> => {
      await delay()
      return mockApps.find(app => app.id === id) || null
    },

    connect: async (appId: string, credentials: any): Promise<{ success: boolean; message: string }> => {
      await delay(2000) // Simulate longer connection time

      // Simulate 80% success rate
      const success = Math.random() > 0.2

      if (success) {
        const appIndex = mockApps.findIndex(app => app.id === appId)
        if (appIndex !== -1) {
          mockApps[appIndex] = {
            ...mockApps[appIndex],
            isConnected: true,
            status: 'connected',
            lastSync: 'Just now',
            connectionDetails: {
              ...credentials,
              connectedAt: new Date().toISOString()
            }
          }
        }
        return { success: true, message: 'Connection successful' }
      } else {
        return { success: false, message: 'Invalid credentials or connection failed' }
      }
    },

    disconnect: async (appId: string): Promise<{ success: boolean; message: string }> => {
      await delay()

      const appIndex = mockApps.findIndex(app => app.id === appId)
      if (appIndex !== -1) {
        mockApps[appIndex] = {
          ...mockApps[appIndex],
          isConnected: false,
          status: 'disconnected',
          lastSync: undefined,
          connectionDetails: undefined
        }
      }

      return { success: true, message: 'App disconnected successfully' }
    },

    testConnection: async (appId: string): Promise<{ success: boolean; message: string }> => {
      await delay(1500)

      const app = mockApps.find(app => app.id === appId)
      if (!app?.isConnected) {
        return { success: false, message: 'App not connected' }
      }

      // Simulate 90% success rate for connection tests
      const success = Math.random() > 0.1
      return {
        success,
        message: success ? 'Connection test successful' : 'Connection test failed'
      }
    }
  },

  // Jobs API
  jobs: {
    getAll: async (): Promise<Job[]> => {
      await delay()
      return [...mockJobs]
    },

    getById: async (id: string): Promise<Job | null> => {
      await delay()
      return mockJobs.find(job => job.id === id) || null
    },

    create: async (jobData: Partial<Job>): Promise<Job> => {
      await delay()

      const newJob: Job = {
        id: Date.now().toString(),
        name: jobData.name || 'New Job',
        description: jobData.description || '',
        app: jobData.app || '',
        appId: jobData.appId || '',
        status: 'paused',
        schedule: jobData.schedule || 'Manual',
        nextRun: 'Paused',
        successRate: 0,
        totalRuns: 0,
        config: {
          syncTypes: [],
          batchSize: 100,
          retryAttempts: 3,
          timeout: 30,
          ...jobData.config
        }
      }

      mockJobs.push(newJob)
      return newJob
    },

    update: async (id: string, updates: Partial<Job>): Promise<Job | null> => {
      await delay()

      const jobIndex = mockJobs.findIndex(job => job.id === id)
      if (jobIndex === -1) return null

      mockJobs[jobIndex] = { ...mockJobs[jobIndex], ...updates }
      return mockJobs[jobIndex]
    },

    delete: async (id: string): Promise<{ success: boolean; message: string }> => {
      await delay()

      const jobIndex = mockJobs.findIndex(job => job.id === id)
      if (jobIndex === -1) {
        return { success: false, message: 'Job not found' }
      }

      mockJobs.splice(jobIndex, 1)
      return { success: true, message: 'Job deleted successfully' }
    },

    toggleStatus: async (id: string, action: 'play' | 'pause'): Promise<Job | null> => {
      await delay()

      const jobIndex = mockJobs.findIndex(job => job.id === id)
      if (jobIndex === -1) return null

      mockJobs[jobIndex] = {
        ...mockJobs[jobIndex],
        status: action === 'play' ? 'running' : 'paused',
        nextRun: action === 'play' ? 'In 5 minutes' : 'Paused'
      }

      return mockJobs[jobIndex]
    },

    duplicate: async (id: string): Promise<Job | null> => {
      await delay()

      const originalJob = mockJobs.find(job => job.id === id)
      if (!originalJob) return null

      const duplicatedJob: Job = {
        ...originalJob,
        id: Date.now().toString(),
        name: `${originalJob.name} (Copy)`,
        status: 'paused',
        nextRun: 'Paused',
        totalRuns: 0,
        successRate: 0
      }

      mockJobs.push(duplicatedJob)
      return duplicatedJob
    },

    getLogs: async (jobId: string): Promise<JobLog[]> => {
      await delay()
      return mockJobLogs.filter(log => log.jobId === jobId)
    }
  },

  // Products API
  products: {
    getAll: async (): Promise<Product[]> => {
      await delay()
      return [...mockProducts]
    },

    sync: async (_appId: string): Promise<{ success: boolean; synced: number; message: string }> => {
      await delay(3000) // Simulate longer sync time

      const syncedCount = Math.floor(Math.random() * 50) + 10
      return {
        success: true,
        synced: syncedCount,
        message: `Successfully synced ${syncedCount} products`
      }
    }
  },

  // Orders API
  orders: {
    getAll: async (): Promise<Order[]> => {
      await delay()
      return [...mockOrders]
    },

    sync: async (_appId: string): Promise<{ success: boolean; synced: number; message: string }> => {
      await delay(2000)

      const syncedCount = Math.floor(Math.random() * 20) + 5
      return {
        success: true,
        synced: syncedCount,
        message: `Successfully synced ${syncedCount} orders`
      }
    }
  },

  // Customers API
  customers: {
    getAll: async (): Promise<Customer[]> => {
      await delay()
      return [...mockCustomers]
    },

    sync: async (_appId: string): Promise<{ success: boolean; synced: number; message: string }> => {
      await delay(1500)

      const syncedCount = Math.floor(Math.random() * 30) + 10
      return {
        success: true,
        synced: syncedCount,
        message: `Successfully synced ${syncedCount} customers`
      }
    }
  },

  // Platform-specific APIs
  shopify: {
    validateCredentials: async (storeUrl: string, accessToken: string): Promise<{ valid: boolean; storeInfo?: any }> => {
      await delay(1500)

      // Basic validation
      if (!storeUrl || !accessToken) {
        return { valid: false }
      }

      if (!storeUrl.includes('.myshopify.com') && !storeUrl.endsWith('.myshopify.com')) {
        return { valid: false }
      }

      if (!accessToken.startsWith('shpat_')) {
        return { valid: false }
      }

      // Simulate 85% success rate
      const valid = Math.random() > 0.15

      if (valid) {
        return {
          valid: true,
          storeInfo: {
            name: storeUrl.split('.')[0],
            email: '<EMAIL>',
            domain: storeUrl,
            plan: 'Basic Shopify'
          }
        }
      }

      return { valid: false }
    },

    getProducts: async (_storeUrl: string, _accessToken: string): Promise<any[]> => {
      await delay(2000)

      return [
        { id: 1, title: 'Sample Product 1', price: '29.99', inventory_quantity: 100 },
        { id: 2, title: 'Sample Product 2', price: '49.99', inventory_quantity: 50 },
        { id: 3, title: 'Sample Product 3', price: '19.99', inventory_quantity: 75 }
      ]
    }
  },

  bigcommerce: {
    validateCredentials: async (storeHash: string, apiToken: string, clientId: string): Promise<{ valid: boolean; storeInfo?: any }> => {
      await delay(1500)

      // Basic validation
      if (!storeHash || !apiToken || !clientId) {
        return { valid: false }
      }

      // Simulate 85% success rate
      const valid = Math.random() > 0.15

      if (valid) {
        return {
          valid: true,
          storeInfo: {
            name: `Store ${storeHash}`,
            email: '<EMAIL>',
            domain: `store-${storeHash}.mybigcommerce.com`,
            plan: 'Standard'
          }
        }
      }

      return { valid: false }
    },

    getProducts: async (_storeHash: string, _apiToken: string): Promise<any[]> => {
      await delay(2000)

      return [
        { id: 1, name: 'BigCommerce Product 1', price: 39.99, inventory_level: 80 },
        { id: 2, name: 'BigCommerce Product 2', price: 59.99, inventory_level: 30 },
        { id: 3, name: 'BigCommerce Product 3', price: 24.99, inventory_level: 60 }
      ]
    }
  }
}

export default mockApiService
