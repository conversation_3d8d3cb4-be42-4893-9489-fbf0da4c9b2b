import { Customer, CustomerFilters, CustomerListResponse, CustomerActivity, CustomerNote, CustomerDocument } from '../types/customer'

// Mock data for customers
const mockCustomers: Customer[] = [
  {
    id: 'CUST-001',
    name: 'Acme Corporation',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Acme Corporation',
    type: 'Business',
    status: 'Active',
    tier: 'Gold',
    credit_limit: 50000,
    credit_used: 15000,
    payment_terms: 'Net 30',
    tax_exempt: false,
    date_created: '2023-01-15',
    last_order_date: '2024-01-10',
    total_orders: 45,
    total_spent: 125000,
    lifetime_value: 150000,
    average_order_value: 2777,
    tags: ['VIP', 'Enterprise', 'Technology'],
    notes: 'Key enterprise customer with high growth potential',
    assigned_rep: 'Sarah <PERSON>',
    territory: 'West Coast',
    source: 'Website',
    website: 'https://acme.com',
    industry: 'Technology',
    employee_count: 500,
    annual_revenue: 10000000,
    primary_contact: {
      id: 'CONT-001',
      name: '<PERSON>',
      title: 'Procurement Manager',
      email: '<EMAIL>',
      phone: '+****************',
      mobile: '+****************',
      department: 'Procurement',
      is_primary: true,
      is_decision_maker: true,
      communication_preferences: ['email', 'phone']
    },
    billing_address: {
      id: 'ADDR-001',
      type: 'billing',
      name: 'Acme Corporation',
      company: 'Acme Corporation',
      address_line_1: '123 Business Ave',
      address_line_2: 'Suite 100',
      city: 'San Francisco',
      state: 'CA',
      postal_code: '94105',
      country: 'USA',
      is_default: true
    },
    shipping_addresses: [
      {
        id: 'ADDR-002',
        type: 'shipping',
        name: 'Acme Warehouse',
        company: 'Acme Corporation',
        address_line_1: '456 Warehouse Blvd',
        city: 'Oakland',
        state: 'CA',
        postal_code: '94607',
        country: 'USA',
        is_default: true,
        delivery_instructions: 'Loading dock B'
      }
    ],
    payment_methods: [
      {
        id: 'PAY-001',
        type: 'net_terms',
        name: 'Net 30 Terms',
        details: 'Net 30 payment terms',
        is_default: true,
        is_active: true
      }
    ],
    credit_history: [
      {
        id: 'CREDIT-001',
        date: '2023-06-15',
        type: 'limit_increase',
        amount: 50000,
        reason: 'Increased order volume',
        approved_by: 'Credit Manager'
      }
    ],
    communication_preferences: {
      email_notifications: true,
      sms_notifications: false,
      phone_calls: true,
      postal_mail: false,
      preferred_contact_time: '9 AM - 5 PM PST',
      language: 'English',
      frequency: 'weekly'
    },
    order_preferences: {
      preferred_shipping_method: 'Standard',
      preferred_payment_method: 'Net Terms',
      auto_reorder: false,
      special_instructions: 'Call before delivery',
      requires_po_number: true,
      approval_required: false
    },
    child_customers: [],
    metrics: {
      orders_last_30_days: 3,
      orders_last_90_days: 8,
      orders_last_year: 45,
      revenue_last_30_days: 8500,
      revenue_last_90_days: 22000,
      revenue_last_year: 125000,
      average_days_between_orders: 8,
      return_rate: 2.1,
      satisfaction_score: 4.8,
      nps_score: 9,
      churn_risk: 'low',
      engagement_score: 85
    }
  },
  {
    id: 'CUST-002',
    name: 'TechStart Inc',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'TechStart Inc',
    type: 'Business',
    status: 'Active',
    tier: 'Silver',
    credit_limit: 25000,
    credit_used: 8500,
    payment_terms: 'Net 15',
    tax_exempt: false,
    date_created: '2023-03-20',
    last_order_date: '2024-01-08',
    total_orders: 28,
    total_spent: 67500,
    lifetime_value: 85000,
    average_order_value: 2410,
    tags: ['Startup', 'Technology', 'Growth'],
    notes: 'Fast-growing startup with increasing order frequency',
    assigned_rep: 'Mike Chen',
    territory: 'East Coast',
    source: 'Referral',
    website: 'https://techstart.com',
    industry: 'Software',
    employee_count: 150,
    annual_revenue: 5000000,
    primary_contact: {
      id: 'CONT-002',
      name: 'Emily Davis',
      title: 'Operations Director',
      email: '<EMAIL>',
      phone: '+****************',
      department: 'Operations',
      is_primary: true,
      is_decision_maker: true,
      communication_preferences: ['email']
    },
    billing_address: {
      id: 'ADDR-003',
      type: 'billing',
      name: 'TechStart Inc',
      company: 'TechStart Inc',
      address_line_1: '789 Innovation Dr',
      city: 'Boston',
      state: 'MA',
      postal_code: '02101',
      country: 'USA',
      is_default: true
    },
    shipping_addresses: [
      {
        id: 'ADDR-004',
        type: 'shipping',
        name: 'TechStart Office',
        company: 'TechStart Inc',
        address_line_1: '789 Innovation Dr',
        city: 'Boston',
        state: 'MA',
        postal_code: '02101',
        country: 'USA',
        is_default: true
      }
    ],
    payment_methods: [
      {
        id: 'PAY-002',
        type: 'credit_card',
        name: 'Corporate Card',
        details: '**** **** **** 1234',
        is_default: true,
        is_active: true,
        expiry_date: '12/25'
      }
    ],
    credit_history: [],
    communication_preferences: {
      email_notifications: true,
      sms_notifications: true,
      phone_calls: false,
      postal_mail: false,
      preferred_contact_time: '10 AM - 6 PM EST',
      language: 'English',
      frequency: 'immediate'
    },
    order_preferences: {
      preferred_shipping_method: 'Express',
      preferred_payment_method: 'Credit Card',
      auto_reorder: true,
      special_instructions: '',
      requires_po_number: false,
      approval_required: false
    },
    child_customers: [],
    metrics: {
      orders_last_30_days: 2,
      orders_last_90_days: 6,
      orders_last_year: 28,
      revenue_last_30_days: 4800,
      revenue_last_90_days: 14500,
      revenue_last_year: 67500,
      average_days_between_orders: 13,
      return_rate: 1.5,
      satisfaction_score: 4.6,
      nps_score: 8,
      churn_risk: 'low',
      engagement_score: 78
    }
  }
]

// Mock activities
const mockActivities: CustomerActivity[] = [
  {
    id: 'ACT-001',
    customer_id: 'CUST-001',
    type: 'order_placed',
    description: 'Order #ORD-003 placed for $2,850',
    date: '2024-01-10T10:30:00Z',
    user: 'System'
  },
  {
    id: 'ACT-002',
    customer_id: 'CUST-001',
    type: 'contact_made',
    description: 'Phone call regarding upcoming order',
    date: '2024-01-08T14:15:00Z',
    user: 'Sarah Johnson'
  }
]

// Mock notes
const mockNotes: CustomerNote[] = [
  {
    id: 'NOTE-001',
    customer_id: 'CUST-001',
    content: 'Customer interested in bulk pricing for Q2 orders',
    type: 'sales',
    visibility: 'internal',
    created_by: 'Sarah Johnson',
    created_at: '2024-01-05T09:00:00Z',
    tags: ['pricing', 'bulk-order']
  }
]

export const mockCustomerApi = {
  getCustomers: async (filters?: CustomerFilters, page = 1, perPage = 20): Promise<CustomerListResponse> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    let filteredCustomers = [...mockCustomers]
    
    // Apply filters
    if (filters?.search) {
      const search = filters.search.toLowerCase()
      filteredCustomers = filteredCustomers.filter(customer =>
        customer.name.toLowerCase().includes(search) ||
        customer.email.toLowerCase().includes(search) ||
        customer.company.toLowerCase().includes(search)
      )
    }
    
    if (filters?.status?.length) {
      filteredCustomers = filteredCustomers.filter(customer =>
        filters.status!.includes(customer.status)
      )
    }
    
    if (filters?.type?.length) {
      filteredCustomers = filteredCustomers.filter(customer =>
        filters.type!.includes(customer.type)
      )
    }
    
    if (filters?.tier?.length) {
      filteredCustomers = filteredCustomers.filter(customer =>
        filters.tier!.includes(customer.tier)
      )
    }
    
    const total = filteredCustomers.length
    const startIndex = (page - 1) * perPage
    const endIndex = startIndex + perPage
    const customers = filteredCustomers.slice(startIndex, endIndex)
    
    return {
      customers,
      total,
      page,
      per_page: perPage,
      total_pages: Math.ceil(total / perPage)
    }
  },

  getCustomer: async (id: string): Promise<Customer | null> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return mockCustomers.find(customer => customer.id === id) || null
  },

  getCustomerActivities: async (customerId: string): Promise<CustomerActivity[]> => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return mockActivities.filter(activity => activity.customer_id === customerId)
  },

  getCustomerNotes: async (customerId: string): Promise<CustomerNote[]> => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return mockNotes.filter(note => note.customer_id === customerId)
  },

  createCustomer: async (customer: Partial<Customer>): Promise<Customer> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    const newCustomer = {
      ...customer,
      id: `CUST-${String(mockCustomers.length + 1).padStart(3, '0')}`,
      date_created: new Date().toISOString().split('T')[0]
    } as Customer
    mockCustomers.push(newCustomer)
    return newCustomer
  },

  updateCustomer: async (id: string, updates: Partial<Customer>): Promise<Customer> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    const index = mockCustomers.findIndex(customer => customer.id === id)
    if (index === -1) throw new Error('Customer not found')
    
    mockCustomers[index] = { ...mockCustomers[index], ...updates }
    return mockCustomers[index]
  },

  deleteCustomer: async (id: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    const index = mockCustomers.findIndex(customer => customer.id === id)
    if (index === -1) throw new Error('Customer not found')
    mockCustomers.splice(index, 1)
  }
}
