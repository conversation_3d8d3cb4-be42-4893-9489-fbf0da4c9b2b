// Mock API services for authentication and multi-tenancy
import { 
  User, 
  Organization, 
  Workspace, 
  Role, 
  UserMembership, 
  Invitation, 
  ActivityLog,
  UserContext 
} from '../types/auth'
import { 
  mockUsers, 
  mockOrganizations, 
  mockWorkspaces, 
  mockRoles, 
  mockUserMemberships, 
  mockInvitations, 
  mockActivityLogs 
} from '../data/mockAuthData'

// Simulate network delay
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// Current user context (simulated session)
let currentUserContext: UserContext | null = null

export const mockAuthApiService = {
  // Authentication
  auth: {
    login: async (email: string, password: string): Promise<{ success: boolean; userContext?: UserContext; message: string }> => {
      await delay(1000)
      
      const user = mockUsers.find(u => u.email === email)
      if (!user || password !== 'password') { // Mock password check
        return { success: false, message: 'Invalid credentials' }
      }

      // Get user memberships
      const userMemberships = mockUserMemberships.filter(m => m.userId === user.id && m.status === 'active')
      if (userMemberships.length === 0) {
        return { success: false, message: 'No workspace access' }
      }

      // Get available organizations and workspaces
      const availableOrganizations = mockOrganizations.filter(org =>
        userMemberships.some(m => m.organizationId === org.id)
      )
      const availableWorkspaces = mockWorkspaces.filter(ws =>
        userMemberships.some(m => m.workspaceId === ws.id)
      )

      // Set default workspace (first one)
      const defaultMembership = userMemberships[0]
      const currentOrganization = availableOrganizations.find(
        org => org.id === defaultMembership.organizationId
      )!
      const currentWorkspace = availableWorkspaces.find(
        ws => ws.id === defaultMembership.workspaceId
      )!
      const currentRole = mockRoles.find(r => r.id === defaultMembership.roleId)!

      const userContext: UserContext = {
        user,
        currentOrganization,
        currentWorkspace,
        currentRole,
        availableOrganizations,
        availableWorkspaces,
        permissions: currentRole.permissions,
        memberships: userMemberships,
        sessionId: 'session_' + Date.now(),
        loginAt: new Date().toISOString(),
        lastActivityAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString()
      }

      currentUserContext = userContext
      return { success: true, userContext, message: 'Login successful' }
    },

    logout: async (): Promise<{ success: boolean; message: string }> => {
      await delay(300)
      currentUserContext = null
      return { success: true, message: 'Logged out successfully' }
    },

    getCurrentUser: async (): Promise<UserContext | null> => {
      await delay(200)
      return currentUserContext
    },

    switchWorkspace: async (organizationId: string, workspaceId: string): Promise<{ success: boolean; userContext?: UserContext; message: string }> => {
      await delay(500)
      
      if (!currentUserContext) {
        return { success: false, message: 'Not authenticated' }
      }

      const membership = currentUserContext.memberships.find(
        m => m.organizationId === organizationId && m.workspaceId === workspaceId && m.status === 'active'
      )

      if (!membership) {
        return { success: false, message: 'Access denied to workspace' }
      }

      const organization = mockOrganizations.find(org => org.id === organizationId)
      const workspace = mockWorkspaces.find(ws => ws.id === workspaceId)
      const role = mockRoles.find(r => r.id === membership.roleId)

      if (!organization || !workspace || !role) {
        return { success: false, message: 'Workspace not found' }
      }

      currentUserContext = {
        ...currentUserContext,
        currentOrganization: organization,
        currentWorkspace: workspace,
        currentRole: role,
        permissions: role.permissions
      }

      return { success: true, userContext: currentUserContext, message: 'Workspace switched successfully' }
    }
  },

  // Organizations
  organizations: {
    getAll: async (): Promise<Organization[]> => {
      await delay()
      if (!currentUserContext) return []
      
      return currentUserContext.availableOrganizations
    },

    getById: async (id: string): Promise<Organization | null> => {
      await delay()
      if (!currentUserContext) return null
      
      return currentUserContext.availableOrganizations.find(org => org.id === id) || null
    },

    update: async (id: string, updates: Partial<Organization>): Promise<{ success: boolean; organization?: Organization; message: string }> => {
      await delay()
      
      const orgIndex = mockOrganizations.findIndex(org => org.id === id)
      if (orgIndex === -1) {
        return { success: false, message: 'Organization not found' }
      }

      mockOrganizations[orgIndex] = { ...mockOrganizations[orgIndex], ...updates, updatedAt: new Date().toISOString() }
      return { success: true, organization: mockOrganizations[orgIndex], message: 'Organization updated successfully' }
    }
  },

  // Workspaces
  workspaces: {
    getAll: async (organizationId?: string): Promise<Workspace[]> => {
      await delay()
      if (!currentUserContext) return []
      
      let workspaces = currentUserContext.availableWorkspaces
      if (organizationId) {
        workspaces = workspaces.filter(ws => ws.organizationId === organizationId)
      }
      
      return workspaces
    },

    getById: async (id: string): Promise<Workspace | null> => {
      await delay()
      if (!currentUserContext) return null
      
      return currentUserContext.availableWorkspaces.find(ws => ws.id === id) || null
    },

    create: async (workspaceData: Partial<Workspace>): Promise<{ success: boolean; workspace?: Workspace; message: string }> => {
      await delay()
      
      if (!currentUserContext) {
        return { success: false, message: 'Not authenticated' }
      }

      const newWorkspace: Workspace = {
        id: 'workspace_' + Date.now(),
        organizationId: workspaceData.organizationId || currentUserContext.currentOrganization.id,
        name: workspaceData.name || 'New Workspace',
        slug: (workspaceData.name || 'new-workspace').toLowerCase().replace(/\s+/g, '-'),
        description: workspaceData.description,
        color: workspaceData.color || '#3B82F6',
        icon: workspaceData.icon || '🏢',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        settings: {
          timezone: 'UTC',
          currency: 'USD',
          dateFormat: 'MM/DD/YYYY',
          allowGuestAccess: false,
          defaultJobSchedule: '0 */6 * * *'
        },
        limits: {
          maxIntegrations: 10,
          maxJobs: 50,
          maxApiCalls: 50000
        },
        usage: {
          integrationCount: 0,
          jobCount: 0,
          apiCallsThisMonth: 0,
          dataPointsThisMonth: 0
        }
      }

      mockWorkspaces.push(newWorkspace)
      return { success: true, workspace: newWorkspace, message: 'Workspace created successfully' }
    },

    update: async (id: string, updates: Partial<Workspace>): Promise<{ success: boolean; workspace?: Workspace; message: string }> => {
      await delay()
      
      const workspaceIndex = mockWorkspaces.findIndex(ws => ws.id === id)
      if (workspaceIndex === -1) {
        return { success: false, message: 'Workspace not found' }
      }

      mockWorkspaces[workspaceIndex] = { ...mockWorkspaces[workspaceIndex], ...updates, updatedAt: new Date().toISOString() }
      return { success: true, workspace: mockWorkspaces[workspaceIndex], message: 'Workspace updated successfully' }
    },

    delete: async (id: string): Promise<{ success: boolean; message: string }> => {
      await delay()
      
      const workspaceIndex = mockWorkspaces.findIndex(ws => ws.id === id)
      if (workspaceIndex === -1) {
        return { success: false, message: 'Workspace not found' }
      }

      mockWorkspaces.splice(workspaceIndex, 1)
      return { success: true, message: 'Workspace deleted successfully' }
    }
  },

  // Users and Memberships
  users: {
    getWorkspaceMembers: async (workspaceId: string): Promise<Array<User & { membership: UserMembership; role: Role }>> => {
      await delay()
      
      const memberships = mockUserMemberships.filter(m => m.workspaceId === workspaceId && m.status === 'active')
      
      return memberships.map(membership => {
        const user = mockUsers.find(u => u.id === membership.userId)!
        const role = mockRoles.find(r => r.id === membership.roleId)!
        return { ...user, membership, role }
      })
    },

    inviteUser: async (invitationData: Partial<Invitation>): Promise<{ success: boolean; invitation?: Invitation; message: string }> => {
      await delay(1000)
      
      if (!currentUserContext) {
        return { success: false, message: 'Not authenticated' }
      }

      // Check if user already exists
      const existingUser = mockUsers.find(u => u.email === invitationData.email)
      if (existingUser) {
        const existingMembership = mockUserMemberships.find(
          m => m.userId === existingUser.id && 
               m.workspaceId === invitationData.workspaceId &&
               m.status === 'active'
        )
        if (existingMembership) {
          return { success: false, message: 'User already has access to this workspace' }
        }
      }

      const newInvitation: Invitation = {
        id: 'invite_' + Date.now(),
        email: invitationData.email!,
        organizationId: invitationData.organizationId || currentUserContext.currentOrganization.id,
        workspaceId: invitationData.workspaceId || currentUserContext.currentWorkspace.id,
        roleId: invitationData.roleId!,
        invitedBy: currentUserContext.user.id,
        invitedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        status: 'pending',
        message: invitationData.message,
        metadata: {
          source: 'manual',
          remindersSent: 0
        }
      }

      mockInvitations.push(newInvitation)
      return { success: true, invitation: newInvitation, message: 'Invitation sent successfully' }
    },

    updateMembership: async (membershipId: string, updates: Partial<UserMembership>): Promise<{ success: boolean; membership?: UserMembership; message: string }> => {
      await delay()
      
      const membershipIndex = mockUserMemberships.findIndex(m => m.id === membershipId)
      if (membershipIndex === -1) {
        return { success: false, message: 'Membership not found' }
      }

      mockUserMemberships[membershipIndex] = { ...mockUserMemberships[membershipIndex], ...updates }
      return { success: true, membership: mockUserMemberships[membershipIndex], message: 'Membership updated successfully' }
    },

    removeMembership: async (membershipId: string): Promise<{ success: boolean; message: string }> => {
      await delay()
      
      const membershipIndex = mockUserMemberships.findIndex(m => m.id === membershipId)
      if (membershipIndex === -1) {
        return { success: false, message: 'Membership not found' }
      }

      mockUserMemberships[membershipIndex].status = 'suspended'
      return { success: true, message: 'User access removed successfully' }
    }
  },

  // Roles
  roles: {
    getAll: async (level?: string): Promise<Role[]> => {
      await delay()
      
      let roles = mockRoles
      if (level) {
        roles = roles.filter(role => role.level === level)
      }
      
      return roles
    },

    getById: async (id: string): Promise<Role | null> => {
      await delay()
      return mockRoles.find(role => role.id === id) || null
    }
  },

  // Activity Logs
  activity: {
    getLogs: async (workspaceId?: string, limit: number = 50): Promise<ActivityLog[]> => {
      await delay()
      
      let logs = mockActivityLogs
      if (workspaceId) {
        logs = logs.filter(log => log.workspaceId === workspaceId)
      }
      
      return logs.slice(0, limit).sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    },

    logActivity: async (activityData: Partial<ActivityLog>): Promise<{ success: boolean; log?: ActivityLog; message: string }> => {
      await delay(200)
      
      if (!currentUserContext) {
        return { success: false, message: 'Not authenticated' }
      }

      const newLog: ActivityLog = {
        id: 'log_' + Date.now(),
        userId: currentUserContext.user.id,
        organizationId: currentUserContext.currentOrganization.id,
        workspaceId: currentUserContext.currentWorkspace.id,
        action: activityData.action!,
        resource: activityData.resource!,
        resourceId: activityData.resourceId,
        details: activityData.details || {},
        ipAddress: '*************', // Mock IP
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        riskLevel: activityData.riskLevel || 'low',
        flagged: false
      }

      mockActivityLogs.push(newLog)
      return { success: true, log: newLog, message: 'Activity logged successfully' }
    }
  }
}

export default mockAuthApiService
