import { api } from '../utils/api'
import {
  Product,
  ProductListItem,
  ProductListResult,
  ProductCategory,
  CreateProductRequest,
  UpdateProductRequest,
  ProductQueryParams
} from '../types/product'

export const productApi = {
  // Get all products with filtering and pagination
  getProducts: async (params?: ProductQueryParams): Promise<ProductListResult> => {
    const searchParams = new URLSearchParams()

    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.category) searchParams.append('category', params.category)
    if (params?.is_visible !== undefined) searchParams.append('is_visible', params.is_visible.toString())
    if (params?.is_featured !== undefined) searchParams.append('is_featured', params.is_featured.toString())
    if (params?.sync_status) searchParams.append('sync_status', params.sync_status)
    if (params?.sort_by) searchParams.append('sort_by', params.sort_by)
    if (params?.sort_order) searchParams.append('sort_order', params.sort_order)

    const queryString = searchParams.toString()
    const url = `/api/products${queryString ? `?${queryString}` : ''}`

    const response = await api.get(url)
    return response.data
  },

  // Get a single product by ID
  getProduct: async (id: string): Promise<Product> => {
    const response = await api.get(`/api/products/${id}`)
    return response.data
  },

  // Create a new product
  createProduct: async (product: CreateProductRequest): Promise<Product> => {
    const response = await api.post('/api/products', product)
    return response.data
  },

  // Update an existing product
  updateProduct: async (id: string, updates: UpdateProductRequest): Promise<Product> => {
    const response = await api.put(`/api/products/${id}`, updates)
    return response.data
  },

  // Delete a product
  deleteProduct: async (id: string): Promise<{ message: string; id: string }> => {
    const response = await api.delete(`/api/products/${id}`)
    return response.data
  },

  // Sync product with BigCommerce
  syncWithBigCommerce: async (id: string): Promise<{ message: string; id: string; status: string }> => {
    const response = await api.post(`/api/products/${id}/sync`)
    return response.data
  },

  // Get all categories
  getCategories: async (): Promise<ProductCategory[]> => {
    const response = await api.get('/api/categories')
    return response.data
  },

  // Bulk operations
  bulkUpdateProducts: async (productIds: string[], updates: UpdateProductRequest): Promise<{ message: string; updated_count: number }> => {
    const response = await api.patch('/api/products/bulk', {
      product_ids: productIds,
      updates
    })
    return response.data
  },

  bulkDeleteProducts: async (productIds: string[]): Promise<{ message: string; deleted_count: number }> => {
    const response = await api.delete('/api/products/bulk', {
      data: { product_ids: productIds }
    })
    return response.data
  },

  bulkSyncProducts: async (productIds: string[]): Promise<{ message: string; synced_count: number }> => {
    const response = await api.post('/api/products/bulk/sync', {
      product_ids: productIds
    })
    return response.data
  }
}

// Mock data for development - 50 products with images
export const mockProductData: ProductListItem[] = [
  {
    id: '1',
    name: 'Wireless Bluetooth Headphones Pro',
    sku: 'WBH-PRO-001',
    price: 199.99,
    inventory_level: 45,
    is_visible: true,
    is_featured: true,
    sync_status: 'synced',
    last_sync_at: '2024-01-15T14:30:00Z',
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-15T14:30:00Z',
    image_url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
    category: 'Electronics',
    brand: 'AudioTech',
    status: 'Active'
  },
  {
    id: '2',
    name: 'Smart Fitness Watch Series 5',
    sku: 'SFW-S5-002',
    price: 299.99,
    inventory_level: 23,
    is_visible: true,
    is_featured: false,
    sync_status: 'pending',
    last_sync_at: '2024-01-14T12:15:00Z',
    created_at: '2024-01-02T10:00:00Z',
    updated_at: '2024-01-14T12:15:00Z',
    image_url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
    category: 'Electronics',
    brand: 'FitTech',
    status: 'Active'
  },
  {
    id: '3',
    name: 'Organic Cotton T-Shirt Premium',
    sku: 'OCT-PREM-003',
    price: 39.99,
    inventory_level: 0,
    is_visible: false,
    is_featured: false,
    sync_status: 'error',
    last_sync_at: '2024-01-13T16:45:00Z',
    created_at: '2024-01-03T10:00:00Z',
    updated_at: '2024-01-13T16:45:00Z',
    image_url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',
    category: 'Clothing',
    brand: 'EcoWear',
    status: 'Inactive'
  },
  {
    id: '4',
    name: 'Premium Coffee Beans Arabica',
    sku: 'PCB-ARA-004',
    price: 34.99,
    inventory_level: 150,
    is_visible: true,
    is_featured: true,
    sync_status: 'synced',
    last_sync_at: '2024-01-16T09:00:00Z',
    created_at: '2024-01-04T10:00:00Z',
    updated_at: '2024-01-16T09:00:00Z',
    image_url: 'https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=400&fit=crop',
    category: 'Food & Beverages',
    brand: 'BrewMaster',
    status: 'Active'
  },
  {
    id: '5',
    name: 'Laptop Stand Adjustable Aluminum',
    sku: 'LSA-ALU-005',
    price: 89.99,
    inventory_level: 32,
    is_visible: true,
    is_featured: false,
    sync_status: 'not_synced',
    created_at: '2024-01-05T10:00:00Z',
    updated_at: '2024-01-05T10:00:00Z',
    image_url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
    category: 'Office Supplies',
    brand: 'WorkSpace',
    status: 'Active'
  },
  {
    id: '6',
    name: 'Wireless Gaming Mouse RGB',
    sku: 'WGM-RGB-006',
    price: 79.99,
    inventory_level: 67,
    is_visible: true,
    is_featured: true,
    sync_status: 'synced',
    last_sync_at: '2024-01-15T11:30:00Z',
    created_at: '2024-01-06T10:00:00Z',
    updated_at: '2024-01-15T11:30:00Z',
    image_url: 'https://images.unsplash.com/photo-1527814050087-3793815479db?w=400&h=400&fit=crop',
    category: 'Electronics',
    brand: 'GameTech',
    status: 'Active'
  },
  {
    id: '7',
    name: 'Stainless Steel Water Bottle',
    sku: 'SSWB-007',
    price: 24.99,
    inventory_level: 89,
    is_visible: true,
    is_featured: false,
    sync_status: 'synced',
    last_sync_at: '2024-01-14T15:20:00Z',
    created_at: '2024-01-07T10:00:00Z',
    updated_at: '2024-01-14T15:20:00Z',
    image_url: 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=400&h=400&fit=crop',
    category: 'Sports & Outdoors',
    brand: 'HydroLife',
    status: 'Active'
  },
  {
    id: '8',
    name: 'Mechanical Keyboard Cherry MX',
    sku: 'MKB-CMX-008',
    price: 149.99,
    inventory_level: 15,
    is_visible: true,
    is_featured: true,
    sync_status: 'pending',
    last_sync_at: '2024-01-13T09:45:00Z',
    created_at: '2024-01-08T10:00:00Z',
    updated_at: '2024-01-13T09:45:00Z',
    image_url: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=400&fit=crop',
    category: 'Electronics',
    brand: 'KeyMaster',
    status: 'Active'
  },
  {
    id: '9',
    name: 'Yoga Mat Premium Non-Slip',
    sku: 'YMP-NS-009',
    price: 49.99,
    inventory_level: 43,
    is_visible: true,
    is_featured: false,
    sync_status: 'synced',
    last_sync_at: '2024-01-16T14:10:00Z',
    created_at: '2024-01-09T10:00:00Z',
    updated_at: '2024-01-16T14:10:00Z',
    image_url: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=400&fit=crop',
    category: 'Sports & Outdoors',
    brand: 'ZenFit',
    status: 'Active'
  },
  {
    id: '10',
    name: 'LED Desk Lamp with USB Charging',
    sku: 'LDL-USB-010',
    price: 59.99,
    inventory_level: 28,
    is_visible: true,
    is_featured: false,
    sync_status: 'error',
    last_sync_at: '2024-01-12T16:30:00Z',
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-01-12T16:30:00Z',
    image_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop',
    category: 'Office Supplies',
    brand: 'BrightWork',
    status: 'Active'
  },
  // Continue with products 11-50
  ...Array.from({ length: 40 }, (_, i) => {
    const id = (i + 11).toString()
    const productNames = [
      'Wireless Earbuds Pro Max', 'Smart Home Speaker', 'Portable Phone Charger', 'Bluetooth Car Adapter',
      'Gaming Headset Surround', 'USB-C Hub Multiport', 'Wireless Phone Stand', 'Smart Light Bulb RGB',
      'Portable Bluetooth Speaker', 'Wireless Charging Pad', 'Smart Doorbell Camera', 'Fitness Tracker Band',
      'Noise Cancelling Headphones', 'Smart Thermostat WiFi', 'Portable Hard Drive 1TB', 'Wireless Security Camera',
      'Smart Plug WiFi Enabled', 'Bluetooth Keyboard Compact', 'Wireless Mouse Ergonomic', 'Smart Watch Sport',
      'Portable Power Bank 20000mAh', 'Wireless Car Charger', 'Smart Air Purifier', 'Bluetooth Shower Speaker',
      'Wireless Webcam HD', 'Smart Coffee Maker', 'Portable Monitor 15inch', 'Wireless Printer All-in-One',
      'Smart Smoke Detector', 'Bluetooth Tracking Tile', 'Wireless Backup Camera', 'Smart Garden Sprinkler',
      'Portable Projector Mini', 'Wireless Doorbell Chime', 'Smart Pet Feeder', 'Bluetooth Sleep Tracker',
      'Wireless Bike Computer', 'Smart Mirror Bathroom', 'Portable Air Conditioner', 'Wireless Weather Station'
    ]

    const categories = ['Electronics', 'Smart Home', 'Accessories', 'Sports & Outdoors', 'Office Supplies']
    const brands = ['TechPro', 'SmartLife', 'ConnectMax', 'PowerTech', 'HomeAuto', 'FitGear', 'WorkFlow']
    const syncStatuses = ['synced', 'pending', 'error', 'not_synced']

    const imageIds = [
      'photo-*************-d8f925fe2cbb', 'photo-*************-5e65395b66cc', 'photo-*************-8f785ba67e45',
      'photo-**********-7eec264c27ff', 'photo-1583394838336-acd977736f90', 'photo-1484704849700-f032a568e944',
      'photo-1560472354-b33ff0c44a43', 'photo-1526170375885-4d8ecf77b99f', 'photo-1505740420928-5e560c06d30e',
      'photo-*************-d8f925fe2cbb', 'photo-*************-5e65395b66cc', 'photo-*************-8f785ba67e45',
      'photo-**********-7eec264c27ff', 'photo-1583394838336-acd977736f90', 'photo-1484704849700-f032a568e944',
      'photo-1560472354-b33ff0c44a43', 'photo-1526170375885-4d8ecf77b99f', 'photo-1505740420928-5e560c06d30e',
      'photo-*************-d8f925fe2cbb', 'photo-*************-5e65395b66cc', 'photo-*************-8f785ba67e45',
      'photo-**********-7eec264c27ff', 'photo-1583394838336-acd977736f90', 'photo-1484704849700-f032a568e944',
      'photo-1560472354-b33ff0c44a43', 'photo-1526170375885-4d8ecf77b99f', 'photo-1505740420928-5e560c06d30e',
      'photo-*************-d8f925fe2cbb', 'photo-*************-5e65395b66cc', 'photo-*************-8f785ba67e45',
      'photo-**********-7eec264c27ff', 'photo-1583394838336-acd977736f90', 'photo-1484704849700-f032a568e944',
      'photo-1560472354-b33ff0c44a43', 'photo-1526170375885-4d8ecf77b99f', 'photo-1505740420928-5e560c06d30e',
      'photo-*************-d8f925fe2cbb', 'photo-*************-5e65395b66cc', 'photo-*************-8f785ba67e45',
      'photo-**********-7eec264c27ff', 'photo-1583394838336-acd977736f90'
    ]

    return {
      id,
      name: productNames[i] || `Product ${id}`,
      sku: `PRD-${id.padStart(3, '0')}`,
      price: Math.round((Math.random() * 500 + 20) * 100) / 100,
      inventory_level: Math.floor(Math.random() * 200),
      is_visible: Math.random() > 0.2,
      is_featured: Math.random() > 0.7,
      sync_status: syncStatuses[Math.floor(Math.random() * syncStatuses.length)] as any,
      last_sync_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      image_url: `https://images.unsplash.com/${imageIds[i]}?w=400&h=400&fit=crop`,
      category: categories[Math.floor(Math.random() * categories.length)],
      brand: brands[Math.floor(Math.random() * brands.length)],
      status: Math.random() > 0.1 ? 'Active' : 'Inactive'
    }
  })
]

export const mockCategories: ProductCategory[] = [
  {
    id: '1',
    name: 'Electronics',
    description: 'Electronic devices and accessories',
    sort_order: 1,
    is_visible: true,
    created_at: '2023-06-01T10:00:00Z'
  },
  {
    id: '2',
    name: 'Clothing',
    description: 'Apparel and fashion items',
    sort_order: 2,
    is_visible: true,
    created_at: '2023-06-01T10:00:00Z'
  },
  {
    id: '3',
    name: 'Food & Beverages',
    description: 'Food and drink products',
    sort_order: 3,
    is_visible: true,
    created_at: '2023-06-01T10:00:00Z'
  },
  {
    id: '4',
    name: 'Office Supplies',
    description: 'Office and workspace equipment',
    sort_order: 4,
    is_visible: true,
    created_at: '2023-06-01T10:00:00Z'
  }
]

// Mock API service for development
export const mockProductApi = {
  getProducts: async (params?: ProductQueryParams): Promise<ProductListResult> => {
    await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

    let filteredProducts = [...mockProductData]

    // Apply filters
    if (params?.search) {
      const search = params.search.toLowerCase()
      filteredProducts = filteredProducts.filter(p =>
        p.name.toLowerCase().includes(search) ||
        p.sku?.toLowerCase().includes(search)
      )
    }

    if (params?.is_visible !== undefined) {
      filteredProducts = filteredProducts.filter(p => p.is_visible === params.is_visible)
    }

    if (params?.is_featured !== undefined) {
      filteredProducts = filteredProducts.filter(p => p.is_featured === params.is_featured)
    }

    if (params?.sync_status) {
      filteredProducts = filteredProducts.filter(p => p.sync_status === params.sync_status)
    }

    // Apply sorting
    if (params?.sort_by) {
      const sortBy = params.sort_by as keyof ProductListItem
      const sortOrder = params.sort_order === 'ASC' ? 1 : -1

      filteredProducts.sort((a, b) => {
        const aVal = a[sortBy]
        const bVal = b[sortBy]

        if (typeof aVal === 'string' && typeof bVal === 'string') {
          return aVal.localeCompare(bVal) * sortOrder
        }

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return (aVal - bVal) * sortOrder
        }

        return 0
      })
    }

    // Apply pagination
    const page = params?.page || 1
    const limit = params?.limit || 20
    const offset = (page - 1) * limit
    const paginatedProducts = filteredProducts.slice(offset, offset + limit)

    return {
      products: paginatedProducts,
      total: filteredProducts.length,
      page,
      limit,
      total_pages: Math.ceil(filteredProducts.length / limit)
    }
  },

  getProduct: async (id: string): Promise<Product> => {
    await new Promise(resolve => setTimeout(resolve, 300))

    const product = mockProductData.find(p => p.id === id)
    if (!product) {
      throw new Error('Product not found')
    }

    // Return a full product with all details
    return {
      ...product,
      product_type: 'physical',
      description: 'This is a sample product description with detailed information about the product features and benefits.',
      weight: 1.5,
      width: 10,
      depth: 8,
      height: 3,
      cost_price: product.price * 0.6,
      retail_price: product.price * 1.2,
      inventory_tracking: 'simple',
      is_free_shipping: false,
      warranty: '1 year manufacturer warranty',
      availability: 'available',
      gift_wrapping_options_type: 'any',
      condition: 'New',
      is_condition_shown: true,
      view_count: Math.floor(Math.random() * 1000),
      is_preorder_only: false,
      open_graph_type: 'product',
      open_graph_use_meta_description: true,
      open_graph_use_product_name: true,
      open_graph_use_image: true,
      total_sold: Math.floor(Math.random() * 100),
      categories: mockCategories.slice(0, 2),
      images: [
        {
          id: '1',
          image_url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
          is_thumbnail: true,
          sort_order: 1,
          created_at: '2023-06-01T10:00:00Z'
        }
      ],
      videos: [],
      variants: [],
      custom_fields: [],
      bulk_pricing_rules: []
    }
  },

  getCategories: async (): Promise<ProductCategory[]> => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return mockCategories
  },

  createProduct: async (product: CreateProductRequest): Promise<Product> => {
    await new Promise(resolve => setTimeout(resolve, 800))

    const newProduct: Product = {
      id: Date.now().toString(),
      ...product,
      view_count: 0,
      total_sold: 0,
      sync_status: 'not_synced',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      categories: [],
      images: product.images || [],
      videos: product.videos || [],
      variants: product.variants || [],
      custom_fields: product.custom_fields || [],
      bulk_pricing_rules: product.bulk_pricing_rules || []
    }

    return newProduct
  },

  updateProduct: async (id: string, updates: UpdateProductRequest): Promise<Product> => {
    await new Promise(resolve => setTimeout(resolve, 600))

    const existingProduct = await mockProductApi.getProduct(id)
    return {
      ...existingProduct,
      ...updates,
      updated_at: new Date().toISOString()
    }
  },

  deleteProduct: async (id: string): Promise<{ message: string; id: string }> => {
    await new Promise(resolve => setTimeout(resolve, 400))
    return {
      message: 'Product deleted successfully',
      id
    }
  },

  syncWithBigCommerce: async (id: string): Promise<{ message: string; id: string; status: string }> => {
    await new Promise(resolve => setTimeout(resolve, 2000))
    return {
      message: 'Product synced successfully with BigCommerce',
      id,
      status: 'synced'
    }
  }
}
