// Core authentication and authorization types

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  avatar?: string
  isActive: boolean
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
  // Global preferences
  preferences: {
    theme: 'light' | 'dark'
    timezone: string
    language: string
    notifications: {
      email: boolean
      push: boolean
      slack: boolean
    }
  }
}

export interface Organization {
  id: string
  name: string
  slug: string
  description?: string
  logo?: string
  website?: string
  industry?: string
  size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
  plan: 'free' | 'starter' | 'professional' | 'enterprise'
  isActive: boolean
  createdAt: string
  updatedAt: string
  // Billing and limits
  billing: {
    customerId?: string
    subscriptionId?: string
    currentPeriodStart: string
    currentPeriodEnd: string
    status: 'active' | 'past_due' | 'canceled' | 'trialing'
  }
  limits: {
    maxWorkspaces: number
    maxUsers: number
    maxIntegrations: number
    maxJobs: number
    dataRetentionDays: number
  }
  settings: {
    allowUserInvites: boolean
    requireTwoFactor: boolean
    allowedDomains: string[]
    sessionTimeoutMinutes: number
  }
}

export interface Workspace {
  id: string
  organizationId: string
  name: string
  slug: string
  description?: string
  color?: string
  icon?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  // Workspace-specific settings
  settings: {
    timezone: string
    currency: string
    dateFormat: string
    allowGuestAccess: boolean
    defaultJobSchedule: string
  }
  // Integration limits per workspace
  limits: {
    maxIntegrations: number
    maxJobs: number
    maxApiCalls: number
  }
  // Usage statistics
  usage: {
    integrationCount: number
    jobCount: number
    apiCallsThisMonth: number
    dataPointsThisMonth: number
    lastActivityAt?: string
  }
}

export interface Permission {
  id: string
  resource: string // 'integrations', 'jobs', 'users', 'billing', 'settings', 'data'
  action: string   // 'create', 'read', 'update', 'delete', 'execute', 'invite', 'export'
  scope: 'own' | 'workspace' | 'organization' | 'global'
  conditions?: {
    // Conditional permissions
    timeRestriction?: {
      startTime: string
      endTime: string
      timezone: string
    }
    ipRestriction?: string[]
    resourceAttributes?: Record<string, any>
  }
}

export interface Role {
  id: string
  name: string
  description: string
  level: 'global' | 'organization' | 'workspace'
  isSystem: boolean // System roles cannot be deleted/modified
  isCustom: boolean
  permissions: Permission[]
  createdAt: string
  updatedAt: string
  // Role metadata
  metadata: {
    color?: string
    icon?: string
    category: 'admin' | 'manager' | 'developer' | 'analyst' | 'viewer' | 'custom'
  }
}

export interface UserMembership {
  id: string
  userId: string
  organizationId?: string
  workspaceId?: string
  roleId: string
  status: 'active' | 'pending' | 'suspended' | 'expired'
  invitedBy?: string
  invitedAt?: string
  joinedAt?: string
  expiresAt?: string
  // Additional permissions beyond role
  additionalPermissions: Permission[]
  // Restrictions
  restrictions: {
    ipWhitelist?: string[]
    allowedHours?: {
      start: string
      end: string
      timezone: string
    }
    maxSessions?: number
  }
}

export interface UserContext {
  user: User
  currentOrganization: Organization
  currentWorkspace: Workspace
  currentRole: Role
  availableOrganizations: Organization[]
  availableWorkspaces: Workspace[]
  permissions: Permission[]
  memberships: UserMembership[]
  // Session info
  sessionId: string
  loginAt: string
  lastActivityAt: string
  expiresAt: string
}

export interface Invitation {
  id: string
  email: string
  organizationId?: string
  workspaceId?: string
  roleId: string
  invitedBy: string
  invitedAt: string
  expiresAt: string
  status: 'pending' | 'accepted' | 'expired' | 'revoked'
  message?: string
  // Invitation metadata
  metadata: {
    source: 'manual' | 'bulk' | 'api' | 'sso'
    remindersSent: number
    lastReminderAt?: string
  }
}

export interface ActivityLog {
  id: string
  userId: string
  organizationId?: string
  workspaceId?: string
  action: string
  resource: string
  resourceId?: string
  details: Record<string, any>
  ipAddress: string
  userAgent: string
  timestamp: string
  // Risk assessment
  riskLevel: 'low' | 'medium' | 'high'
  flagged: boolean
}

// Predefined system roles
export const SYSTEM_ROLES = {
  // Global roles
  SUPER_ADMIN: 'super_admin',
  SUPPORT_AGENT: 'support_agent',
  
  // Organization roles
  ORG_OWNER: 'org_owner',
  ORG_ADMIN: 'org_admin',
  BILLING_MANAGER: 'billing_manager',
  
  // Workspace roles
  WORKSPACE_OWNER: 'workspace_owner',
  WORKSPACE_ADMIN: 'workspace_admin',
  INTEGRATION_MANAGER: 'integration_manager',
  DEVELOPER: 'developer',
  ANALYST: 'analyst',
  VIEWER: 'viewer'
} as const

// Permission resources
export const RESOURCES = {
  INTEGRATIONS: 'integrations',
  JOBS: 'jobs',
  USERS: 'users',
  BILLING: 'billing',
  SETTINGS: 'settings',
  DATA: 'data',
  AUDIT: 'audit',
  API: 'api'
} as const

// Permission actions
export const ACTIONS = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  EXECUTE: 'execute',
  INVITE: 'invite',
  EXPORT: 'export',
  IMPORT: 'import',
  APPROVE: 'approve',
  CONFIGURE: 'configure'
} as const

// Permission scopes
export const SCOPES = {
  OWN: 'own',
  WORKSPACE: 'workspace',
  ORGANIZATION: 'organization',
  GLOBAL: 'global'
} as const
