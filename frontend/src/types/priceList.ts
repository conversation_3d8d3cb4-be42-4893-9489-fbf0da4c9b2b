// Price List management types

export interface Channel {
  id: string
  name: string
  description?: string
  isActive: boolean
}

export interface CustomerGroup {
  id: string
  name: string
  description?: string
  channel_id: string
}

export interface Supplier {
  id: string
  name: string
  contact_email?: string
  contact_phone?: string
  is_primary?: boolean
}

export interface Classification {
  id: string
  name: string
  description?: string
  parent_id?: string
}

export interface Tag {
  id: string
  name: string
  color?: string
}

export interface PriceListItem {
  id: string
  product_id: string
  product_name: string
  sku: string
  classification?: Classification
  current_stock: number
  cost: number
  
  // Pricing by customer group and channel
  pricing: {
    [customerGroupId: string]: {
      [channelId: string]: number
    }
  }
  
  // Sales data by channel
  sales_data: {
    [channelId: string]: {
      csp_count: number // Unique Customer Count
      monthly_sales: {
        jan: number
        feb: number
        mar: number
        apr: number
        may: number
        jun: number
      }
    }
  }
  
  weeks_on_hand: number
  turn_rate: number
  purchaser: string
  supplier: Supplier
  last_updated_by: string
  date_created: string
  last_updated_date: string
  tags: Tag[]
}

export interface PriceListFilters {
  purchaser?: string[]
  primary_supplier?: string[]
  classification?: string[]
  tags?: string[]
  classified_as?: string[]
  products?: string[]
  top_products?: 'top_10' | 'top_50' | 'top_100' | 'top_200' | 'top_500'
  cost_margin?: string
}

export interface PriceListQueryParams {
  search?: string
  channel?: string | string[]
  filters?: PriceListFilters
  page?: number
  limit?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface PriceListResponse {
  items: PriceListItem[]
  total: number
  page: number
  limit: number
  total_pages: number
}

export interface ColumnConfig {
  key: string
  label: string
  visible: boolean
  width?: number
  sticky?: boolean
}

export interface CSVExportOptions {
  export_type: 'all' | 'visible' | 'filtered'
  include_headers: boolean
  selected_columns?: string[]
}

export interface CSVImportResult {
  success: boolean
  total_rows: number
  imported_rows: number
  errors: {
    row: number
    field: string
    message: string
  }[]
}
