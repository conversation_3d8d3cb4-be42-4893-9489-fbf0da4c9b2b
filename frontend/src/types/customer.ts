export interface Customer {
  id: string
  name: string
  email: string
  phone: string
  company: string
  type: 'Individual' | 'Business' | 'Enterprise' | 'Government'
  status: 'Active' | 'Inactive' | 'Suspended' | 'Pending'
  tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond'
  credit_limit: number
  credit_used: number
  payment_terms: string
  tax_exempt: boolean
  date_created: string
  last_order_date: string
  total_orders: number
  total_spent: number
  lifetime_value: number
  average_order_value: number
  tags: string[]
  notes: string
  assigned_rep: string
  territory: string
  source: string
  website: string
  industry: string
  employee_count: number
  annual_revenue: number
  
  // Contact Information
  primary_contact: Contact
  billing_contact?: Contact
  shipping_contact?: Contact
  
  // Addresses
  billing_address: Address
  shipping_addresses: Address[]
  
  // Financial
  payment_methods: PaymentMethod[]
  credit_history: CreditHistory[]
  
  // Preferences
  communication_preferences: CommunicationPreferences
  order_preferences: OrderPreferences
  
  // Relationships
  parent_customer_id?: string
  child_customers: string[]
  
  // Metrics
  metrics: CustomerMetrics
}

export interface Contact {
  id: string
  name: string
  title: string
  email: string
  phone: string
  mobile?: string
  department?: string
  is_primary: boolean
  is_decision_maker: boolean
  communication_preferences: string[]
}

export interface Address {
  id: string
  type: 'billing' | 'shipping' | 'both'
  name: string
  company?: string
  address_line_1: string
  address_line_2?: string
  city: string
  state: string
  postal_code: string
  country: string
  is_default: boolean
  delivery_instructions?: string
  access_code?: string
}

export interface PaymentMethod {
  id: string
  type: 'credit_card' | 'bank_transfer' | 'check' | 'net_terms' | 'cash'
  name: string
  details: string
  is_default: boolean
  is_active: boolean
  expiry_date?: string
}

export interface CreditHistory {
  id: string
  date: string
  type: 'credit_check' | 'limit_increase' | 'limit_decrease' | 'payment_default'
  amount: number
  reason: string
  approved_by: string
  notes?: string
}

export interface CommunicationPreferences {
  email_notifications: boolean
  sms_notifications: boolean
  phone_calls: boolean
  postal_mail: boolean
  preferred_contact_time: string
  language: string
  frequency: 'immediate' | 'daily' | 'weekly' | 'monthly'
}

export interface OrderPreferences {
  preferred_shipping_method: string
  preferred_payment_method: string
  auto_reorder: boolean
  special_instructions: string
  requires_po_number: boolean
  approval_required: boolean
  price_list_id?: string
}

export interface CustomerMetrics {
  orders_last_30_days: number
  orders_last_90_days: number
  orders_last_year: number
  revenue_last_30_days: number
  revenue_last_90_days: number
  revenue_last_year: number
  average_days_between_orders: number
  return_rate: number
  satisfaction_score: number
  nps_score: number
  churn_risk: 'low' | 'medium' | 'high'
  engagement_score: number
}

export interface CustomerActivity {
  id: string
  customer_id: string
  type: 'order_placed' | 'payment_received' | 'contact_made' | 'quote_sent' | 'meeting_scheduled' | 'note_added' | 'status_changed'
  description: string
  date: string
  user: string
  metadata?: Record<string, any>
}

export interface CustomerNote {
  id: string
  customer_id: string
  content: string
  type: 'general' | 'sales' | 'support' | 'billing' | 'internal'
  visibility: 'public' | 'internal'
  created_by: string
  created_at: string
  updated_at?: string
  tags: string[]
}

export interface CustomerDocument {
  id: string
  customer_id: string
  name: string
  type: 'contract' | 'agreement' | 'certificate' | 'license' | 'other'
  file_url: string
  file_size: number
  uploaded_by: string
  uploaded_at: string
  expiry_date?: string
  status: 'active' | 'expired' | 'pending_renewal'
}

export interface CustomerFilters {
  search?: string
  status?: string[]
  type?: string[]
  tier?: string[]
  territory?: string[]
  assigned_rep?: string[]
  tags?: string[]
  credit_status?: string[]
  last_order_from?: string
  last_order_to?: string
  total_spent_min?: number
  total_spent_max?: number
  date_created_from?: string
  date_created_to?: string
}

export interface CustomerListResponse {
  customers: Customer[]
  total: number
  page: number
  per_page: number
  total_pages: number
}
