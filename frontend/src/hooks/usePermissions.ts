import { useMemo } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { createPermissionChecker } from '../utils/permissions'
import { RESOURCES, ACTIONS } from '../types/auth'

// Custom hook for permission checking
export const usePermissions = () => {
  const {
    user,
    currentOrganization,
    currentWorkspace,
    currentRole,
    permissions,
    memberships,
    isAuthenticated
  } = useAuth()

  // Create permission checker instance
  const permissionChecker = useMemo(() => {
    if (!isAuthenticated || !user || !currentOrganization || !currentWorkspace || !currentRole) {
      return null
    }

    return createPermissionChecker({
      user,
      currentOrganization,
      currentWorkspace,
      currentRole,
      availableOrganizations: [],
      availableWorkspaces: [],
      permissions,
      memberships,
      sessionId: '',
      loginAt: '',
      lastActivityAt: '',
      expiresAt: ''
    })
  }, [isAuthenticated, user, currentOrganization, currentWorkspace, currentRole, permissions, memberships])

  // Permission checking functions
  const hasPermission = (resource: string, action: string, scope?: string): boolean => {
    return permissionChecker?.hasPermission(resource, action, scope) ?? false
  }

  const hasAnyPermission = (permissionChecks: Array<{ resource: string; action: string; scope?: string }>): boolean => {
    return permissionChecker?.hasAnyPermission(permissionChecks) ?? false
  }

  const hasAllPermissions = (permissionChecks: Array<{ resource: string; action: string; scope?: string }>): boolean => {
    return permissionChecker?.hasAllPermissions(permissionChecks) ?? false
  }

  const hasRole = (roleId: string): boolean => {
    return permissionChecker?.hasRole(roleId) ?? false
  }

  const hasAnyRole = (roleIds: string[]): boolean => {
    return permissionChecker?.hasAnyRole(roleIds) ?? false
  }

  const canAccessWorkspace = (workspaceId: string): boolean => {
    return permissionChecker?.canAccessWorkspace(workspaceId) ?? false
  }

  const canAccessOrganization = (organizationId: string): boolean => {
    return permissionChecker?.canAccessOrganization(organizationId) ?? false
  }

  // Specific permission helpers
  const canManageIntegrations = (): boolean => {
    return hasAnyPermission([
      { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.CREATE },
      { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.UPDATE },
      { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.DELETE }
    ])
  }

  const canViewIntegrations = (): boolean => {
    return hasPermission(RESOURCES.INTEGRATIONS, ACTIONS.READ)
  }

  const canCreateIntegrations = (): boolean => {
    return hasPermission(RESOURCES.INTEGRATIONS, ACTIONS.CREATE)
  }

  const canConfigureIntegrations = (): boolean => {
    return hasPermission(RESOURCES.INTEGRATIONS, ACTIONS.CONFIGURE)
  }

  const canManageJobs = (): boolean => {
    return hasAnyPermission([
      { resource: RESOURCES.JOBS, action: ACTIONS.CREATE },
      { resource: RESOURCES.JOBS, action: ACTIONS.UPDATE },
      { resource: RESOURCES.JOBS, action: ACTIONS.DELETE }
    ])
  }

  const canViewJobs = (): boolean => {
    return hasPermission(RESOURCES.JOBS, ACTIONS.READ)
  }

  const canCreateJobs = (): boolean => {
    return hasPermission(RESOURCES.JOBS, ACTIONS.CREATE)
  }

  const canExecuteJobs = (): boolean => {
    return hasPermission(RESOURCES.JOBS, ACTIONS.EXECUTE)
  }

  const canManageUsers = (): boolean => {
    return hasAnyPermission([
      { resource: RESOURCES.USERS, action: ACTIONS.INVITE },
      { resource: RESOURCES.USERS, action: ACTIONS.UPDATE },
      { resource: RESOURCES.USERS, action: ACTIONS.DELETE }
    ])
  }

  const canViewUsers = (): boolean => {
    return hasPermission(RESOURCES.USERS, ACTIONS.READ)
  }

  const canInviteUsers = (): boolean => {
    return hasPermission(RESOURCES.USERS, ACTIONS.INVITE)
  }

  const canViewData = (): boolean => {
    return hasPermission(RESOURCES.DATA, ACTIONS.READ)
  }

  const canExportData = (): boolean => {
    return hasPermission(RESOURCES.DATA, ACTIONS.EXPORT)
  }

  const canImportData = (): boolean => {
    return hasPermission(RESOURCES.DATA, ACTIONS.IMPORT)
  }

  const canViewSettings = (): boolean => {
    return hasPermission(RESOURCES.SETTINGS, ACTIONS.READ)
  }

  const canManageSettings = (): boolean => {
    return hasPermission(RESOURCES.SETTINGS, ACTIONS.UPDATE)
  }

  const canViewBilling = (): boolean => {
    return hasPermission(RESOURCES.BILLING, ACTIONS.READ)
  }

  const canManageBilling = (): boolean => {
    return hasPermission(RESOURCES.BILLING, ACTIONS.UPDATE)
  }

  const canViewAudit = (): boolean => {
    return hasPermission(RESOURCES.AUDIT, ACTIONS.READ)
  }

  // Role checking helpers
  const isWorkspaceOwner = (): boolean => {
    return hasRole('role_workspace_owner')
  }

  const isWorkspaceAdmin = (): boolean => {
    return hasAnyRole(['role_workspace_owner', 'role_workspace_admin'])
  }

  const isIntegrationManager = (): boolean => {
    return hasRole('role_integration_manager')
  }

  const isDeveloper = (): boolean => {
    return hasRole('role_developer')
  }

  const isAnalyst = (): boolean => {
    return hasRole('role_analyst')
  }

  const isViewer = (): boolean => {
    return hasRole('role_viewer')
  }

  const isAdmin = (): boolean => {
    return hasAnyRole([
      'super_admin',
      'org_owner',
      'org_admin',
      'role_workspace_owner',
      'role_workspace_admin'
    ])
  }

  const isManager = (): boolean => {
    return hasAnyRole([
      'role_integration_manager',
      'role_workspace_owner',
      'role_workspace_admin'
    ])
  }

  return {
    // Core permission checker
    permissionChecker,

    // Basic permission functions
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    canAccessWorkspace,
    canAccessOrganization,

    // Integration permissions
    canManageIntegrations,
    canViewIntegrations,
    canCreateIntegrations,
    canConfigureIntegrations,

    // Job permissions
    canManageJobs,
    canViewJobs,
    canCreateJobs,
    canExecuteJobs,

    // User permissions
    canManageUsers,
    canViewUsers,
    canInviteUsers,

    // Data permissions
    canViewData,
    canExportData,
    canImportData,

    // Settings permissions
    canViewSettings,
    canManageSettings,

    // Billing permissions
    canViewBilling,
    canManageBilling,

    // Audit permissions
    canViewAudit,

    // Role helpers
    isWorkspaceOwner,
    isWorkspaceAdmin,
    isIntegrationManager,
    isDeveloper,
    isAnalyst,
    isViewer,
    isAdmin,
    isManager
  }
}

export default usePermissions
