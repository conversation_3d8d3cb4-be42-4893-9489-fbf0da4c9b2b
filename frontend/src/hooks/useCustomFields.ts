import { useState, useEffect } from 'react'

interface CustomField {
  id: string
  label: string
  key: string
  field_type: string
  resource_type: string
  namespace: string
  description?: string
  placeholder_text?: string
  help_text?: string
  is_required: boolean
  is_searchable: boolean
  is_filterable: boolean
  is_ai_enabled: boolean
  is_active: boolean
  field_options?: any
  validation_rules?: any
}

interface UseCustomFieldsProps {
  resourceType: string
  resourceId?: string
}

interface UseCustomFieldsReturn {
  fields: CustomField[]
  values: { [key: string]: any }
  loading: boolean
  error: string | null
  updateValue: (key: string, value: any) => void
  saveValues: () => Promise<void>
  addField: (field: Omit<CustomField, 'id'>) => Promise<void>
  removeField: (fieldId: string) => Promise<void>
  refreshFields: () => Promise<void>
}

export const useCustomFields = ({ resourceType, resourceId }: UseCustomFieldsProps): UseCustomFieldsReturn => {
  const [fields, setFields] = useState<CustomField[]>([])
  const [values, setValues] = useState<{ [key: string]: any }>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Sample data - replace with actual API calls
  const sampleFields: CustomField[] = [
    {
      id: '1',
      label: 'SEO Title',
      key: 'seo_title',
      field_type: 'text',
      resource_type: 'products',
      namespace: 'global',
      description: 'Custom SEO title for search engines',
      placeholder_text: 'Enter SEO title...',
      help_text: 'This will be used as the page title in search results',
      is_required: false,
      is_searchable: true,
      is_filterable: false,
      is_ai_enabled: true,
      is_active: true,
      field_options: {},
      validation_rules: { max_length: 60 }
    },
    {
      id: '2',
      label: 'Warranty Period',
      key: 'warranty_period',
      field_type: 'number',
      resource_type: 'products',
      namespace: 'global',
      description: 'Warranty period in months',
      placeholder_text: '12',
      help_text: 'Enter warranty period in months',
      is_required: false,
      is_searchable: false,
      is_filterable: true,
      is_ai_enabled: false,
      is_active: true,
      field_options: {},
      validation_rules: { min: 0, max: 120 }
    },
    {
      id: '3',
      label: 'Product Category',
      key: 'product_category',
      field_type: 'single_select',
      resource_type: 'products',
      namespace: 'global',
      description: 'Product category classification',
      placeholder_text: '',
      help_text: 'Select the primary category for this product',
      is_required: true,
      is_searchable: false,
      is_filterable: true,
      is_ai_enabled: false,
      is_active: true,
      field_options: {
        options: [
          { value: 'electronics', label: 'Electronics' },
          { value: 'clothing', label: 'Clothing' },
          { value: 'home', label: 'Home & Garden' },
          { value: 'sports', label: 'Sports & Outdoors' },
          { value: 'books', label: 'Books' }
        ]
      },
      validation_rules: {}
    },
    {
      id: '4',
      label: 'Tags',
      key: 'tags',
      field_type: 'multi_select',
      resource_type: 'products',
      namespace: 'global',
      description: 'Product tags for organization',
      placeholder_text: '',
      help_text: 'Select multiple tags that apply to this product',
      is_required: false,
      is_searchable: true,
      is_filterable: true,
      is_ai_enabled: false,
      is_active: true,
      field_options: {
        options: [
          { value: 'new', label: 'New' },
          { value: 'featured', label: 'Featured' },
          { value: 'sale', label: 'On Sale' },
          { value: 'bestseller', label: 'Bestseller' },
          { value: 'limited', label: 'Limited Edition' },
          { value: 'eco', label: 'Eco-Friendly' }
        ]
      },
      validation_rules: {}
    },
    {
      id: '5',
      label: 'Is Featured',
      key: 'is_featured',
      field_type: 'boolean',
      resource_type: 'products',
      namespace: 'global',
      description: 'Mark product as featured',
      placeholder_text: '',
      help_text: 'Featured products appear prominently on the homepage',
      is_required: false,
      is_searchable: false,
      is_filterable: true,
      is_ai_enabled: false,
      is_active: true,
      field_options: {},
      validation_rules: {}
    },
    {
      id: '6',
      label: 'Launch Date',
      key: 'launch_date',
      field_type: 'date',
      resource_type: 'products',
      namespace: 'global',
      description: 'Product launch date',
      placeholder_text: '',
      help_text: 'When this product was first launched',
      is_required: false,
      is_searchable: false,
      is_filterable: true,
      is_ai_enabled: false,
      is_active: true,
      field_options: {},
      validation_rules: {}
    }
  ]

  // Sample values - replace with actual API calls
  const sampleValues = {
    seo_title: 'Premium Wireless Headphones - Best Sound Quality',
    warranty_period: 24,
    product_category: 'electronics',
    tags: ['new', 'featured'],
    is_featured: true,
    launch_date: '2024-01-15'
  }

  const fetchFields = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/custom-fields?resource_type=${resourceType}`)
      // const data = await response.json()
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const relevantFields = sampleFields.filter(field => 
        field.resource_type === resourceType || field.resource_type === 'global'
      )
      
      setFields(relevantFields)
    } catch (err) {
      setError('Failed to fetch custom fields')
      console.error('Error fetching custom fields:', err)
    } finally {
      setLoading(false)
    }
  }

  const fetchValues = async () => {
    if (!resourceId) return

    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/custom-field-values?resource_type=${resourceType}&resource_id=${resourceId}`)
      // const data = await response.json()
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300))
      
      setValues(sampleValues)
    } catch (err) {
      console.error('Error fetching custom field values:', err)
    }
  }

  const updateValue = (key: string, value: any) => {
    setValues(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const saveValues = async () => {
    if (!resourceId) return

    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/custom-field-values`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     resource_type: resourceType,
      //     resource_id: resourceId,
      //     values
      //   })
      // })
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      console.log('Custom field values saved:', values)
    } catch (err) {
      console.error('Error saving custom field values:', err)
      throw err
    }
  }

  const addField = async (field: Omit<CustomField, 'id'>) => {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch('/api/custom-fields', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(field)
      // })
      // const newField = await response.json()
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newField = {
        ...field,
        id: Date.now().toString()
      }
      
      setFields(prev => [...prev, newField])
    } catch (err) {
      console.error('Error adding custom field:', err)
      throw err
    }
  }

  const removeField = async (fieldId: string) => {
    try {
      // TODO: Replace with actual API call
      // await fetch(`/api/custom-fields/${fieldId}`, { method: 'DELETE' })
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300))
      
      setFields(prev => prev.filter(field => field.id !== fieldId))
      
      // Remove the value if it exists
      const fieldToRemove = fields.find(f => f.id === fieldId)
      if (fieldToRemove) {
        setValues(prev => {
          const newValues = { ...prev }
          delete newValues[fieldToRemove.key]
          return newValues
        })
      }
    } catch (err) {
      console.error('Error removing custom field:', err)
      throw err
    }
  }

  const refreshFields = async () => {
    await fetchFields()
    if (resourceId) {
      await fetchValues()
    }
  }

  useEffect(() => {
    fetchFields()
  }, [resourceType])

  useEffect(() => {
    if (resourceId) {
      fetchValues()
    }
  }, [resourceId])

  return {
    fields,
    values,
    loading,
    error,
    updateValue,
    saveValues,
    addField,
    removeField,
    refreshFields
  }
}
