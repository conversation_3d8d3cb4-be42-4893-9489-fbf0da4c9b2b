import { useState, useRef, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  Settings,
  Clock,
  Users,
  Shield,
  Bell,
  Database,
  Play,
  Pause,
  Plus,
  Layers,
  ChevronDown,
  Check
} from 'lucide-react'
import JobsList from '../components/JobsList'
import CustomFieldsPage from './CustomFieldsPage'
import toast from 'react-hot-toast'

interface Job {
  id: string
  name: string
  description: string
  app: string
  status: 'running' | 'paused' | 'error' | 'completed'
  schedule: string
  lastRun?: string
  nextRun?: string
  duration?: string
  successRate?: number
  totalRuns?: number
}

interface CustomDropdownProps {
  value: string
  options: string[]
  onChange: (value: string) => void
  className?: string
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({ value, options, onChange, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-48 px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 flex items-center justify-between transition-colors"
      >
        <span className="text-gray-900">{value}</span>
        <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${
          isOpen ? 'transform rotate-180' : ''
        }`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
          {options.map((option) => (
            <button
              key={option}
              onClick={() => {
                onChange(option)
                setIsOpen(false)
              }}
              className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 transition-colors flex items-center justify-between ${
                value === option ? 'bg-blue-50 text-blue-700' : 'text-gray-900'
              }`}
            >
              <span>{option}</span>
              {value === option && <Check className="h-4 w-4 text-blue-600" />}
            </button>
          ))}
        </div>
      )}
    </div>
  )
}

const SettingsPage = () => {
  const location = useLocation()
  const navigate = useNavigate()

  // Determine active tab from URL
  const getActiveTab = () => {
    if (location.pathname.includes('/jobs')) return 'jobs'
    if (location.pathname.includes('/users')) return 'users'
    if (location.pathname.includes('/custom-fields')) return 'custom-fields'
    if (location.pathname.includes('/security')) return 'security'
    if (location.pathname.includes('/notifications')) return 'notifications'
    if (location.pathname.includes('/database')) return 'database'
    if (location.pathname.includes('/general')) return 'general'
    return 'general' // default
  }

  const [activeTab, setActiveTab] = useState(getActiveTab())

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getActiveTab())
  }, [location.pathname])

  // Handle tab navigation
  const handleTabChange = (tabId: string) => {
    const routes = {
      general: '/settings/general',
      jobs: '/settings/jobs',
      users: '/settings/users/my-organization/users',
      'custom-fields': '/settings/custom-fields',
      security: '/settings/security',
      notifications: '/settings/notifications',
      database: '/settings/database'
    }
    navigate(routes[tabId as keyof typeof routes] || '/settings/general')
  }

  // Settings state
  const [language, setLanguage] = useState('English (US)')
  const [timezone, setTimezone] = useState('UTC-8 (Pacific Time)')
  const [dateFormat, setDateFormat] = useState('MM/DD/YYYY')
  const [sessionTimeout, setSessionTimeout] = useState('30 minutes')
  const [dataRetention, setDataRetention] = useState('30 days')

  const [jobs, setJobs] = useState<Job[]>([
    {
      id: '1',
      name: 'Shopify Product Sync',
      description: 'Sync products from Shopify store every hour',
      app: 'Shopify',
      status: 'running',
      schedule: 'Every hour',
      lastRun: '2 minutes ago',
      nextRun: 'In 58 minutes',
      duration: '45s',
      successRate: 98,
      totalRuns: 156
    },
    {
      id: '2',
      name: 'BigCommerce Order Import',
      description: 'Import new orders from BigCommerce',
      app: 'BigCommerce',
      status: 'paused',
      schedule: 'Every 30 minutes',
      lastRun: '1 hour ago',
      nextRun: 'Paused',
      duration: '1m 23s',
      successRate: 95,
      totalRuns: 89
    },
    {
      id: '3',
      name: 'Customer Data Sync',
      description: 'Sync customer information across platforms',
      app: 'Multiple',
      status: 'error',
      schedule: 'Daily at 2:00 AM',
      lastRun: '6 hours ago',
      nextRun: 'In 18 hours',
      duration: 'Failed',
      successRate: 76,
      totalRuns: 23
    }
  ])

  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'jobs', name: 'Jobs', icon: Clock },
    { id: 'users', name: 'User Management', icon: Users },
    { id: 'custom-fields', name: 'Custom Fields', icon: Layers },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'database', name: 'Database', icon: Database },
  ]

  // Job handler functions
  const handleToggleJob = (jobId: string, action: 'play' | 'pause') => {
    setJobs(prevJobs =>
      prevJobs.map(job =>
        job.id === jobId
          ? {
              ...job,
              status: action === 'play' ? 'running' : 'paused',
              nextRun: action === 'play' ? 'In 5 minutes' : 'Paused'
            }
          : job
      )
    )
    toast.success(`Job ${action === 'play' ? 'started' : 'paused'} successfully`)
  }

  const handleEditJob = (_jobId: string) => {
    console.log('Edit job functionality coming soon')
  }

  const handleDeleteJob = (jobId: string) => {
    setJobs(prevJobs => prevJobs.filter(job => job.id !== jobId))
    toast.success('Job deleted successfully')
  }

  const handleDuplicateJob = (jobId: string) => {
    const jobToDuplicate = jobs.find(job => job.id === jobId)
    if (jobToDuplicate) {
      const newJob = {
        ...jobToDuplicate,
        id: Date.now().toString(),
        name: `${jobToDuplicate.name} (Copy)`,
        status: 'paused' as const,
        nextRun: 'Paused'
      }
      setJobs(prevJobs => [...prevJobs, newJob])
      toast.success('Job duplicated successfully')
    }
  }



  const renderJobsContent = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Sync Jobs</h2>
          <p className="mt-1 text-gray-600">
            Manage automated data synchronization jobs
          </p>
        </div>
        <button className="btn btn-primary flex items-center">
          <Plus className="h-4 w-4 mr-2" />
          Create Job
        </button>
      </div>

      {/* Jobs Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card">
          <div className="flex items-center">
            <Play className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Running</p>
              <p className="text-2xl font-bold text-gray-900">
                {jobs.filter(job => job.status === 'running').length}
              </p>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <Pause className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Paused</p>
              <p className="text-2xl font-bold text-gray-900">
                {jobs.filter(job => job.status === 'paused').length}
              </p>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <div className="h-8 w-8 bg-red-500 rounded-full" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Errors</p>
              <p className="text-2xl font-bold text-gray-900">
                {jobs.filter(job => job.status === 'error').length}
              </p>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <Clock className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Jobs</p>
              <p className="text-2xl font-bold text-gray-900">{jobs.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Jobs List */}
      <JobsList
        jobs={jobs}
        onToggleJob={handleToggleJob}
        onEditJob={handleEditJob}
        onDeleteJob={handleDeleteJob}
        onDuplicateJob={handleDuplicateJob}
      />
    </div>
  )



  const renderGeneralContent = () => (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-semibold text-gray-900">General Settings</h2>
        <p className="mt-2 text-gray-600">Manage your application preferences and system configuration</p>
      </div>

      {/* Application Preferences */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="p-6 border-b border-gray-100">
          <h3 className="text-lg font-medium text-gray-900">Application Preferences</h3>
          <p className="text-sm text-gray-500 mt-1">Customize your application experience</p>
        </div>
        <div className="p-6 space-y-6">
          {/* Language Setting */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Language</label>
              <p className="text-sm text-gray-500">Choose your preferred language</p>
            </div>
            <CustomDropdown
              value={language}
              options={['English (US)', 'English (UK)', 'Spanish', 'French']}
              onChange={setLanguage}
            />
          </div>

          {/* Timezone Setting */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Timezone</label>
              <p className="text-sm text-gray-500">Set your local timezone</p>
            </div>
            <CustomDropdown
              value={timezone}
              options={['UTC-8 (Pacific Time)', 'UTC-5 (Eastern Time)', 'UTC+0 (GMT)', 'UTC+1 (Central European)']}
              onChange={setTimezone}
            />
          </div>

          {/* Date Format */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Date Format</label>
              <p className="text-sm text-gray-500">Choose how dates are displayed</p>
            </div>
            <CustomDropdown
              value={dateFormat}
              options={['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']}
              onChange={setDateFormat}
            />
          </div>
        </div>
      </div>

      {/* System Configuration */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="p-6 border-b border-gray-100">
          <h3 className="text-lg font-medium text-gray-900">System Configuration</h3>
          <p className="text-sm text-gray-500 mt-1">Configure system-wide settings</p>
        </div>
        <div className="p-6 space-y-6">
          {/* Auto-save */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Auto-save</label>
              <p className="text-sm text-gray-500">Automatically save changes</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" defaultChecked />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {/* Session Timeout */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Session Timeout</label>
              <p className="text-sm text-gray-500">Automatically log out after inactivity</p>
            </div>
            <CustomDropdown
              value={sessionTimeout}
              options={['30 minutes', '1 hour', '2 hours', '4 hours', 'Never']}
              onChange={setSessionTimeout}
            />
          </div>

          {/* Data Retention */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Data Retention</label>
              <p className="text-sm text-gray-500">How long to keep sync logs</p>
            </div>
            <CustomDropdown
              value={dataRetention}
              options={['30 days', '90 days', '6 months', '1 year']}
              onChange={setDataRetention}
            />
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          Save Changes
        </button>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeTab) {
      case 'jobs':
        return renderJobsContent()
      case 'general':
        return renderGeneralContent()
      case 'custom-fields':
        return <CustomFieldsPage />
      default:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {tabs.find(tab => tab.id === activeTab)?.name}
              </h2>
              <p className="mt-1 text-gray-600">This section is coming soon.</p>
            </div>
            <div className="card">
              <p className="text-gray-600">Content for {activeTab} will be implemented here.</p>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="-m-6 min-h-screen bg-gray-50">
      <div className="flex">
        {/* Modern Sidebar */}
        <div className="w-72 bg-white border-r border-gray-200 min-h-screen">
          <div className="p-6 border-b border-gray-100">
            <h1 className="text-xl font-semibold text-gray-900">Settings</h1>
            <p className="text-sm text-gray-500 mt-1">Manage your application preferences</p>
          </div>
          <nav className="p-4 space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200 shadow-sm'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <Icon className={`h-5 w-5 mr-3 ${
                    activeTab === tab.id ? 'text-blue-600' : 'text-gray-400'
                  }`} />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Modern Main Content */}
        <div className="flex-1">
          <div className="p-8">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage
