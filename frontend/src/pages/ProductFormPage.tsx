import { useState, useEffect } from 'react'
import { Save, ArrowLeft, Package, Settings } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import CustomFieldRenderer from '../components/CustomFieldRenderer'
import CreateCustomFieldModal from '../components/CreateCustomFieldModal'
import FieldManagementModal from '../components/FieldManagementModal'
import { useCustomFields } from '../hooks/useCustomFields'
import toast from 'react-hot-toast'

interface ProductFormData {
  name: string
  description: string
  price: string
  sku: string
  category: string
  status: string
}

const ProductFormPage: React.FC = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    price: '',
    sku: '',
    category: '',
    status: 'active'
  })

  const [showCustomFieldModal, setShowCustomFieldModal] = useState(false)
  const [showFieldManagementModal, setShowFieldManagementModal] = useState(false)
  const [editingField, setEditingField] = useState(null)
  const [saving, setSaving] = useState(false)

  // Use custom fields hook
  const {
    fields,
    values: customFieldValues,
    loading: fieldsLoading,
    updateValue: updateCustomFieldValue,
    saveValues: saveCustomFieldValues,
    addField,
    removeField,
    refreshFields
  } = useCustomFields({ resourceType: 'products', resourceId: 'new' })

  // State for active fields in this form
  const [activeFields, setActiveFields] = useState(fields)

  // Sync activeFields with fields from hook
  useEffect(() => {
    setActiveFields(fields)
  }, [fields])

  const handleInputChange = (field: keyof ProductFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      // Validate required fields
      if (!formData.name.trim()) {
        toast.error('Product name is required')
        return
      }

      // Validate required custom fields
      const requiredFields = fields.filter(field => field.is_required)
      for (const field of requiredFields) {
        if (!customFieldValues[field.key]) {
          toast.error(`${field.label} is required`)
          return
        }
      }

      // Save product data
      console.log('Saving product:', formData)
      
      // Save custom field values
      await saveCustomFieldValues()
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('Product saved successfully!')
      navigate('/products')
    } catch (error) {
      toast.error('Failed to save product')
      console.error('Save error:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleAddCustomField = () => {
    setShowFieldManagementModal(true)
  }

  const handleCreateNewField = () => {
    setShowFieldManagementModal(false)
    setEditingField(null)
    setShowCustomFieldModal(true)
  }

  const handleFieldSettings = (field: any) => {
    setEditingField(field)
    setShowCustomFieldModal(true)
  }

  const handleSaveCustomField = async (fieldData: any) => {
    try {
      if (editingField) {
        // Update existing field logic would go here
        toast.success('Custom field updated successfully')
      } else {
        await addField({
          ...fieldData,
          resource_type: 'products'
        })
        toast.success('Custom field added successfully')
        // Refresh the available fields and add the new field to active fields
        await refreshFields()
      }
      setShowCustomFieldModal(false)
      setEditingField(null)
    } catch (error) {
      toast.error('Failed to save custom field')
    }
  }

  const handleFieldsChange = (newFields: any[]) => {
    setActiveFields(newFields)
  }

  const handleRemoveField = async (fieldId: string) => {
    if (window.confirm('Are you sure you want to remove this field?')) {
      try {
        await removeField(fieldId)
        toast.success('Custom field removed successfully')
      } catch (error) {
        toast.error('Failed to remove custom field')
      }
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/products')}
                className="text-gray-400 hover:text-gray-600"
              >
                <ArrowLeft className="h-6 w-6" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">Create Product</h1>
                  <p className="text-sm text-gray-500">Add a new product to your catalog</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={() => navigate('/products')}
                className="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSave}
                disabled={saving}
                className="btn btn-primary flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : 'Save Product'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter product name..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SKU
                </label>
                <input
                  type="text"
                  value={formData.sku}
                  onChange={(e) => handleInputChange('sku', e.target.value)}
                  placeholder="Enter SKU..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price
                </label>
                <input
                  type="number"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  placeholder="0.00"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="active">Active</option>
                  <option value="draft">Draft</option>
                  <option value="archived">Archived</option>
                </select>
              </div>
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter product description..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Custom Fields */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            {fieldsLoading ? (
              <div className="animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="space-y-4">
                  {[1, 2, 3].map(i => (
                    <div key={i}>
                      <div className="h-4 bg-gray-200 rounded w-1/6 mb-2"></div>
                      <div className="h-10 bg-gray-200 rounded"></div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <CustomFieldRenderer
                fields={activeFields}
                values={customFieldValues}
                onChange={updateCustomFieldValue}
                resourceType="products"
                showFieldManagement={true}
                onAddField={handleAddCustomField}
                onFieldSettings={handleFieldSettings}
                onRemoveField={handleRemoveField}
              />
            )}
          </div>
        </div>
      </div>

      {/* Field Management Modal */}
      <FieldManagementModal
        isOpen={showFieldManagementModal}
        onClose={() => setShowFieldManagementModal(false)}
        resourceType="products"
        activeFields={activeFields}
        onFieldsChange={handleFieldsChange}
        onCreateNewField={handleCreateNewField}
      />

      {/* Custom Field Modal */}
      <CreateCustomFieldModal
        isOpen={showCustomFieldModal}
        onClose={() => {
          setShowCustomFieldModal(false)
          setEditingField(null)
        }}
        onSave={handleSaveCustomField}
        editingField={editingField}
      />
    </div>
  )
}

export default ProductFormPage
