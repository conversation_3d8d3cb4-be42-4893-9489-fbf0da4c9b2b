import { useState } from 'react'
import { useParams, useNavigate, useLocation } from 'react-router-dom'
import {
  ArrowLeft,
  Mail,
  Edit,
  ExternalLink,
  Building,
  User,
  Shield,
  Settings,
  Activity,
  Phone,
  Calendar,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,

  Key,
  LogOut,
  Trash2,
  Save,
  X
} from 'lucide-react'

const UserDetailPage = () => {
  const { userId } = useParams()
  const navigate = useNavigate()
  const location = useLocation()
  const [activeTab, setActiveTab] = useState('overview')

  console.log('UserDetailPage rendered, userId:', userId)

  // Determine if this is a guest user based on the URL
  const isGuestUser = location.pathname.includes('/guest-organization/')

  // Mock user data - in real app this would come from API based on userId and type
  const getUserData = () => {
    if (isGuestUser) {
      // Guest user data
      if (userId === 'guest-1') {
        return {
          id: 'guest-1',
          name: '<PERSON>',
          email: '<EMAIL>',
          phone: '+****************',
          organization: 'TechStart Inc',
          accessLevel: 'Read Only',
          status: 'Active',
          dateGranted: 'May 15, 2025',
          expirationDate: 'Jun 15, 2025',
          lastLogin: 'May 20, 2025, 03:15 PM',
          lastIpAddress: '************',
          avatar: 'MW',
          description: 'Guest User from TechStart Inc • Granted: May 15, 2025',
          permissions: {
            dashboard: 'Read Only',
            orders: 'No Access',
            products: 'Read Only'
          }
        }
      } else if (userId === 'guest-2') {
        return {
          id: 'guest-2',
          name: 'Anna Lee',
          email: '<EMAIL>',
          phone: '+****************',
          organization: 'Global Corp',
          accessLevel: 'Limited Access',
          status: 'Pending',
          dateGranted: 'May 20, 2025',
          expirationDate: 'Jul 01, 2025',
          lastLogin: 'Never',
          lastIpAddress: 'N/A',
          avatar: 'AL',
          description: 'Guest User from Global Corp • Granted: May 20, 2025',
          permissions: {
            dashboard: 'Limited',
            orders: 'Limited',
            products: 'No Access'
          }
        }
      } else {
        return {
          id: 'guest-3',
          name: 'Robert Kim',
          email: '<EMAIL>',
          phone: '+****************',
          organization: 'Innovation Labs',
          accessLevel: 'Full Access',
          status: 'Expired',
          dateGranted: 'Mar 01, 2025',
          expirationDate: 'May 01, 2025',
          lastLogin: 'Apr 30, 2025, 11:45 AM',
          lastIpAddress: '*************',
          avatar: 'RK',
          description: 'Guest User from Innovation Labs • Granted: Mar 01, 2025',
          permissions: {
            dashboard: 'Full Access',
            orders: 'Full Access',
            products: 'Full Access'
          }
        }
      }
    } else {
      // Internal user data
      return {
        id: userId,
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '+****************',
        role: 'Executive',
        userRole: 'Admin',
        status: 'Active',
        twoFactorEnabled: true,
        dateCreated: 'Oct 15, 2024',
        lastUpdated: 'May 10, 2025',
        lastLogin: 'May 21, 2025, 02:30 PM',
        lastIpAddress: '***********',
        accountStatus: 'Active',
        avatar: 'JS',
        description: 'Internal User since Oct 15, 2024 • ID: #' + userId,
        permissions: {
          dashboard: 'Full Access',
          orders: 'Limited',
          products: 'Limited'
        }
      }
    }
  }

  const user = getUserData()

  const loginHistory = [
    {
      date: 'May 21, 2025, 02:30 PM',
      ipAddress: '***********',
      device: 'Chrome / Windows 10',
      location: 'San Francisco, CA, USA',
      status: 'Success'
    },
    {
      date: 'May 18, 2025, 09:45 AM',
      ipAddress: '***********',
      device: 'Chrome / Windows 10',
      location: 'San Francisco, CA, USA',
      status: 'Success'
    },
    {
      date: 'May 16, 2025, 04:20 PM',
      ipAddress: '***********',
      device: 'Mobile / iOS 17',
      location: 'San Francisco, CA, USA',
      status: 'Success'
    },
    {
      date: 'May 10, 2025, 11:15 AM',
      ipAddress: '***********',
      device: 'Chrome / Windows 10',
      location: 'San Francisco, CA, USA',
      status: 'Success'
    },
    {
      date: 'May 5, 2025, 08:30 AM',
      ipAddress: '***********',
      device: 'Firefox / macOS',
      location: 'San Francisco, CA, USA',
      status: 'Failed'
    }
  ]

  const activityLog = [
    {
      id: 1,
      action: 'Updated profile information',
      timestamp: 'May 15, 2025, 10:30 AM',
      details: 'By: Sarah Johnson',
      type: 'profile'
    },
    {
      id: 2,
      action: 'Permissions updated by Administrator',
      timestamp: 'May 10, 2025, 02:20 PM',
      details: 'By: Sarah Johnson',
      type: 'permissions'
    },
    {
      id: 3,
      action: 'Role changed from Manager to Admin',
      timestamp: 'May 1, 2025, 09:15 AM',
      details: 'By: Sarah Johnson',
      type: 'role'
    },
    {
      id: 4,
      action: 'Account created',
      timestamp: 'Oct 15, 2024, 08:50 AM',
      details: '',
      type: 'account'
    }
  ]

  const tabs = [
    { id: 'overview', name: 'Overview', icon: User },
    { id: 'permissions', name: 'Permissions', icon: Shield },
    { id: 'security', name: 'Security', icon: Settings },
    { id: 'activity', name: 'Activity', icon: Activity }
  ]

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
    switch (status.toLowerCase()) {
      case 'active':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case 'expired':
      case 'inactive':
        return `${baseClasses} bg-red-100 text-red-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  const getPermissionBadge = (permission: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
    switch (permission.toLowerCase()) {
      case 'full access':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'limited':
      case 'read only':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case 'no access':
        return `${baseClasses} bg-red-100 text-red-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'profile':
        return <User className="h-4 w-4 text-blue-500" />
      case 'permissions':
        return <Shield className="h-4 w-4 text-purple-500" />
      case 'role':
        return <Key className="h-4 w-4 text-orange-500" />
      case 'account':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => navigate(-1)}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to {isGuestUser ? 'Guest Organization' : 'My Organization'}
          </button>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`h-12 w-12 rounded-full flex items-center justify-center text-white font-medium text-lg ${
                isGuestUser ? 'bg-indigo-500' : 'bg-green-500'
              }`}>
                {user.avatar}
              </div>
              <div className="ml-4">
                <h1 className="text-2xl font-bold text-gray-900">{user.name}</h1>
                <p className="text-sm text-gray-500">{user.description}</p>
                <div className="flex items-center mt-1">
                  <span className={getStatusBadge(user.status)}>{user.status}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {isGuestUser && (
                <div className="flex items-center text-sm text-gray-500">
                  <Building className="h-4 w-4 mr-1" />
                  {user.organization}
                  <ExternalLink className="h-3 w-3 ml-1" />
                </div>
              )}
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <Mail className="h-4 w-4 mr-2" />
                Email User
              </button>
              <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800">
                <Edit className="h-4 w-4 mr-2" />
                Edit User
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {tab.name}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Personal Information */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200 flex items-center">
                    <User className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">Personal Information</h3>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center mb-6">
                      <div className={`h-16 w-16 rounded-full flex items-center justify-center text-white font-medium text-xl ${
                        isGuestUser ? 'bg-indigo-500' : 'bg-green-500'
                      }`}>
                        {user.avatar}
                      </div>
                      <div className="ml-4">
                        <h4 className="text-lg font-medium text-gray-900">{user.name}</h4>
                        <p className="text-sm text-gray-500">{user.role || 'Executive'}</p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">{user.email}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">{user.phone}</span>
                      </div>
                      {isGuestUser && (
                        <div className="flex items-center">
                          <Building className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-sm text-gray-900">{user.organization}</span>
                        </div>
                      )}
                    </div>

                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-500">User Role</span>
                        <span className="text-sm text-gray-900">{user.userRole || 'Admin'}</span>
                      </div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-500">Status</span>
                        <span className={getStatusBadge(user.status)}>{user.status}</span>
                      </div>
                      {!isGuestUser && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-500">2FA Enabled</span>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            user.twoFactorEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {user.twoFactorEnabled ? 'Enabled' : 'Disabled'}
                          </span>
                        </div>
                      )}
                    </div>

                    <button className="mt-6 w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Information
                    </button>
                  </div>
                </div>

                {/* Account Information */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200 flex items-center">
                    <Settings className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">Account Information</h3>
                  </div>
                  <div className="p-6">
                    <div className="space-y-4">
                      <div>
                        <dt className="text-sm font-medium text-gray-500">User ID</dt>
                        <dd className="mt-1 text-sm text-gray-900">#{user.id}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Date Created</dt>
                        <dd className="mt-1 text-sm text-gray-900 flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                          {user.dateCreated || 'Oct 15, 2024'}
                        </dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd className="mt-1 text-sm text-gray-900 flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                          {user.lastUpdated || 'May 10, 2025'}
                        </dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Last Login</dt>
                        <dd className="mt-1 text-sm text-gray-900 flex items-center">
                          <Clock className="h-4 w-4 text-gray-400 mr-2" />
                          {user.lastLogin}
                        </dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Last IP Address</dt>
                        <dd className="mt-1 text-sm text-gray-900 flex items-center">
                          <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                          {user.lastIpAddress}
                        </dd>
                      </div>
                    </div>

                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="flex items-center justify-between mb-4">
                        <span className="text-sm font-medium text-gray-500">Account Status</span>
                        <span className={getStatusBadge(user.accountStatus || user.status)}>
                          {user.accountStatus || user.status}
                        </span>
                      </div>
                      <button className="w-full inline-flex items-center justify-center px-4 py-2 border border-blue-300 shadow-sm text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100">
                        <Edit className="h-4 w-4 mr-2" />
                        Change Status
                      </button>
                    </div>
                  </div>
                </div>

                {/* Role Assignment */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200 flex items-center">
                    <Shield className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">Role Assignment</h3>
                  </div>
                  <div className="p-6">
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-500">User Role</span>
                        <span className="text-sm font-medium text-gray-900">{user.userRole || 'Admin'}</span>
                      </div>
                      <p className="text-sm text-gray-600">Full access to all features</p>
                    </div>

                    <div className="mb-6">
                      <h4 className="text-sm font-medium text-gray-900 mb-3">Role Permissions Summary</h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Dashboard</span>
                          <span className={getPermissionBadge(user.permissions.dashboard)}>
                            {user.permissions.dashboard}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Orders</span>
                          <span className={getPermissionBadge(user.permissions.orders)}>
                            {user.permissions.orders}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Products</span>
                          <span className={getPermissionBadge(user.permissions.products)}>
                            {user.permissions.products}
                          </span>
                        </div>
                      </div>
                    </div>

                    <button className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800">
                      <Save className="h-4 w-4 mr-2" />
                      Save Role Changes
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'permissions' && (
              <div>
                <div className="mb-6 flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">User Permissions</h3>
                    <p className="text-sm text-gray-500">Manage specific permissions for this user</p>
                  </div>
                  <div className="flex space-x-3">
                    <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      Restore Defaults
                    </button>
                    <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800">
                      Save Changes
                    </button>
                  </div>
                </div>

                {/* Dashboard Permissions */}
                <div className="mb-8">
                  <h4 className="text-base font-medium text-gray-900 mb-4">Dashboard</h4>
                  <p className="text-sm text-gray-600 mb-4">Analytics dashboards and reports</p>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">View Dashboard</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={true}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">Edit Dashboard</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={true}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">Export Dashboard Data</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={true}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Orders Permissions */}
                <div className="mb-8">
                  <h4 className="text-base font-medium text-gray-900 mb-4">Orders</h4>
                  <p className="text-sm text-gray-600 mb-4">Order management and processing</p>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">View Orders</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={true}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">Create Orders</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={false}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">Edit Orders</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={true}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">Delete Orders</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={false}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">Export Orders</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={true}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Products Permissions */}
                <div className="mb-8">
                  <h4 className="text-base font-medium text-gray-900 mb-4">Products</h4>
                  <p className="text-sm text-gray-600 mb-4">Product catalog and inventory</p>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">View Products</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={true}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">Create Products</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={false}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">Edit Products</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={false}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <span className="text-sm font-medium text-gray-900">Delete Products</span>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked={false}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'security' && (
              <div className="space-y-8">
                {/* Account Security */}
                <div>
                  <div className="flex items-center mb-6">
                    <Shield className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">Account Security</h3>
                  </div>

                  <div className="space-y-6">
                    {/* Password Reset */}
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Password Reset</h4>
                        <p className="text-sm text-gray-600">Send a password reset link to the user</p>
                      </div>
                      <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Reset Password
                      </button>
                    </div>

                    {/* Two-Factor Authentication */}
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
                        <p className="text-sm text-gray-600">User has 2FA enabled for their account</p>
                      </div>
                      <button className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100">
                        <X className="h-4 w-4 mr-1" />
                        Disable 2FA
                      </button>
                    </div>

                    {/* Force Logout */}
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Force Logout</h4>
                        <p className="text-sm text-gray-600">Log user out of all active sessions</p>
                      </div>
                      <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <LogOut className="h-4 w-4 mr-1" />
                        Force Logout
                      </button>
                    </div>
                  </div>
                </div>

                {/* Security History */}
                <div>
                  <div className="flex items-center mb-6">
                    <Clock className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">Security History</h3>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center p-4 bg-green-50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-green-900">Successful login</p>
                        <p className="text-sm text-green-700">Chrome / Windows 10 • IP: ***********</p>
                      </div>
                      <span className="text-sm text-green-600">May 21, 2025, 02:30 PM</span>
                    </div>

                    <div className="flex items-center p-4 bg-green-50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-green-900">Password changed</p>
                        <p className="text-sm text-green-700">User changed password</p>
                      </div>
                      <span className="text-sm text-green-600">May 1, 2025, 10:15 AM</span>
                    </div>

                    <div className="flex items-center p-4 bg-blue-50 rounded-lg">
                      <Shield className="h-5 w-5 text-blue-500 mr-3" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-900">Two-factor authentication enabled</p>
                        <p className="text-sm text-blue-700">User enabled two-factor authentication</p>
                      </div>
                      <span className="text-sm text-blue-600">Apr 15, 2025, 02:20 PM</span>
                    </div>

                    <div className="flex items-center p-4 bg-red-50 rounded-lg">
                      <XCircle className="h-5 w-5 text-red-500 mr-3" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-red-900">Failed login attempt</p>
                        <p className="text-sm text-red-700">Firefox / macOS • IP: ***********</p>
                      </div>
                      <span className="text-sm text-red-600">May 5, 2025, 08:30 AM</span>
                    </div>
                  </div>
                </div>

                {/* Danger Zone */}
                <div className="border-t border-gray-200 pt-8">
                  <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-red-900 mb-2">Danger Zone</h3>
                    <p className="text-sm text-red-700 mb-6">These actions can't be undone. Please be careful.</p>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-red-900">Deactivate Account</h4>
                          <p className="text-sm text-red-700">Temporarily disable this user's access to the system</p>
                        </div>
                        <button className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50">
                          <X className="h-4 w-4 mr-1" />
                          Deactivate
                        </button>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-red-900">Delete User</h4>
                          <p className="text-sm text-red-700">Permanently delete this user and all associated data</p>
                        </div>
                        <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                          <Trash2 className="h-4 w-4 mr-1" />
                          Delete User
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'activity' && (
              <div className="space-y-8">
                {/* User Activity Log */}
                <div>
                  <div className="flex items-center mb-6">
                    <Activity className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">User Activity Log</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-6">Comprehensive history of user actions and system changes</p>

                  <div className="space-y-4">
                    {activityLog.map((activity) => (
                      <div key={activity.id} className="flex items-start p-4 bg-gray-50 rounded-lg">
                        <div className="mr-3 mt-0.5">
                          {getActivityIcon(activity.type)}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                          <p className="text-sm text-gray-600">{activity.details}</p>
                        </div>
                        <span className="text-sm text-gray-500">{activity.timestamp}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Login History */}
                <div>
                  <div className="flex items-center mb-6">
                    <Clock className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">Login History</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-6">Recent login attempts and sessions</p>

                  <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table className="min-w-full divide-y divide-gray-300">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date & Time
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            IP Address
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Location
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Device / Browser
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {loginHistory.map((login, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {login.date}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {login.ipAddress}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 flex items-center">
                              <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                              {login.location}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {login.device}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                login.status === 'Success'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {login.status === 'Success' ? (
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                ) : (
                                  <XCircle className="h-3 w-3 mr-1" />
                                )}
                                {login.status}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserDetailPage
