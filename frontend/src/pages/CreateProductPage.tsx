import { useState, useRef, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useMutation, useQuery } from 'react-query'
import {
  ArrowLeft,
  Save,
  Package,
  DollarSign,
  Truck,
  Eye,
  Search,
  Image,
  Settings,
  BarChart3,
  Star,
  Palette
} from 'lucide-react'
import { mockProductApi } from '../services/productApi'
import { CreateProductRequest } from '../types/product'
import CustomSelect from '../components/CustomSelect'
import toast from 'react-hot-toast'

const CreateProductPage = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEditMode = Boolean(id)
  const [activeTab, setActiveTab] = useState('basic')

  // Form state with all BigCommerce fields
  const [formData, setFormData] = useState<CreateProductRequest>({
    name: '',
    product_type: 'physical',
    sku: '',
    description: '',
    weight: 0,
    width: 0,
    depth: 0,
    height: 0,
    price: 0,
    cost_price: 0,
    retail_price: 0,
    sale_price: 0,
    map_price: 0,
    tax_class_id: 0,
    product_tax_code: '',
    brand_id: 0,
    brand_name: '',
    inventory_level: 0,
    inventory_warning_level: 0,
    inventory_tracking: 'none',
    fixed_cost_shipping_price: 0,
    is_free_shipping: false,
    is_visible: true,
    is_featured: false,
    warranty: '',
    bin_picking_number: '',
    layout_file: '',
    upc: '',
    search_keywords: '',
    availability_description: '',
    availability: 'available',
    gift_wrapping_options_type: 'any',
    sort_order: 0,
    condition: 'New',
    is_condition_shown: true,
    order_quantity_minimum: 1,
    order_quantity_maximum: 0,
    page_title: '',
    meta_description: '',
    preorder_release_date: '',
    preorder_message: '',
    is_preorder_only: false,
    is_price_hidden: false,
    price_hidden_label: '',
    open_graph_type: 'product',
    open_graph_title: '',
    open_graph_description: '',
    open_graph_use_meta_description: true,
    open_graph_use_product_name: true,
    open_graph_use_image: true,
    gtin: '',
    mpn: '',
    categories: [],
    images: [],
    videos: [],
    variants: [],
    custom_fields: [],
    bulk_pricing_rules: [],
    // Additional fields for new sections
    price_includes_tax: false,
    image_alt_text: '',
    video_url: '',
    variant_option_name: '',
    variant_option_values: '',
    variant_display_type: 'dropdown',
    variant_price_modifier_type: 'fixed',
    variant_price_modifier: 0,
    variant_track_inventory: false,
    variant_required: false,
    related_products: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Get categories for dropdown
  const { data: categories = [] } = useQuery('categories', () => mockProductApi.getCategories())

  // Fetch product data when in edit mode
  const { data: productData, isLoading: isLoadingProduct } = useQuery(
    ['product', id],
    () => mockProductApi.getProduct(id!),
    {
      enabled: isEditMode,
      onSuccess: (data) => {
        // Populate form with existing product data
        setFormData(prev => ({
          ...prev,
          ...data
        }))
      }
    }
  )

  const saveProductMutation = useMutation(
    (productData: CreateProductRequest) => {
      if (isEditMode) {
        return mockProductApi.updateProduct(id!, productData)
      } else {
        return mockProductApi.createProduct(productData)
      }
    },
    {
      onSuccess: () => {
        toast.success(isEditMode ? 'Product updated successfully!' : 'Product created successfully!')
        navigate('/products')
      },
      onError: (error: any) => {
        toast.error(isEditMode ? 'Failed to update product' : 'Failed to create product')
        console.error('Save product error:', error)
      }
    }
  )

  const handleInputChange = (field: keyof CreateProductRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required'
    }

    if (formData.price <= 0) {
      newErrors.price = 'Price must be greater than 0'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      toast.error('Please fix the errors in the form')
      return
    }

    saveProductMutation.mutate(formData)
  }

  const tabs = [
    { id: 'basic', label: 'Basic Information', icon: Package },
    { id: 'pricing', label: 'Pricing', icon: DollarSign },
    { id: 'inventory', label: 'Inventory', icon: BarChart3 },
    { id: 'shipping', label: 'Shipping', icon: Truck },
    { id: 'seo', label: 'SEO & URLs', icon: Search },
    { id: 'images', label: 'Images & Videos', icon: Image },
    { id: 'variants', label: 'Variants', icon: Palette },
    { id: 'advanced', label: 'Advanced', icon: Settings }
  ]

  const renderBasicSection = () => {
    return (
      <div className="space-y-6">
        {/* Product Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Name *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter product name"
          />
          {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
        </div>

        {/* Product Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Type
          </label>
          <CustomSelect
            options={[
              { value: 'physical', label: 'Physical Product' },
              { value: 'digital', label: 'Digital Product' }
            ]}
            value={formData.product_type}
            onChange={(value) => handleInputChange('product_type', value)}
            placeholder="Select product type"
          />
        </div>

        {/* SKU */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            SKU
          </label>
          <input
            type="text"
            value={formData.sku}
            onChange={(e) => handleInputChange('sku', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter SKU"
          />
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            rows={6}
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter product description (HTML supported)"
          />
        </div>

        {/* Categories */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Categories
          </label>
          <CustomSelect
            options={categories.map(cat => ({
              value: cat.id,
              label: cat.name
            }))}
            value=""
            onChange={(value) => {
              const currentCategories = formData.categories || []
              if (!currentCategories.includes(value)) {
                handleInputChange('categories', [...currentCategories, value])
              }
            }}
            placeholder="Select categories"
          />
          {formData.categories && formData.categories.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-2">
              {formData.categories.map((catId) => {
                const category = categories.find(c => c.id === catId)
                return (
                  <span
                    key={catId}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {category?.name}
                    <button
                      type="button"
                      onClick={() => {
                        const newCategories = formData.categories?.filter(id => id !== catId) || []
                        handleInputChange('categories', newCategories)
                      }}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                )
              })}
            </div>
          )}
        </div>

        {/* Brand */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Brand ID
            </label>
            <input
              type="number"
              value={formData.brand_id || ''}
              onChange={(e) => handleInputChange('brand_id', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter brand ID"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Brand Name
            </label>
            <input
              type="text"
              value={formData.brand_name}
              onChange={(e) => handleInputChange('brand_name', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter brand name"
            />
          </div>
        </div>

        {/* Condition */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Condition
            </label>
            <CustomSelect
              options={[
                { value: 'New', label: 'New' },
                { value: 'Used', label: 'Used' },
                { value: 'Refurbished', label: 'Refurbished' }
              ]}
              value={formData.condition}
              onChange={(value) => handleInputChange('condition', value)}
              placeholder="Select condition"
            />
          </div>

          <div className="flex items-center pt-8">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_condition_shown}
                onChange={(e) => handleInputChange('is_condition_shown', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Show condition on storefront</span>
            </label>
          </div>
        </div>

        {/* Visibility Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.is_visible}
              onChange={(e) => handleInputChange('is_visible', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <Eye className="h-4 w-4 ml-2 mr-1 text-gray-500" />
            <span className="text-sm text-gray-700">Visible on storefront</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.is_featured}
              onChange={(e) => handleInputChange('is_featured', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <Star className="h-4 w-4 ml-2 mr-1 text-gray-500" />
            <span className="text-sm text-gray-700">Featured product</span>
          </label>
        </div>
      </div>
    )
  }

  const renderPricingSection = () => {
    return (
      <div className="space-y-6">
        {/* Main Pricing */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Price *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                className={`w-full pl-7 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.price ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="0.00"
              />
            </div>
            {errors.price && <p className="mt-1 text-sm text-red-600">{errors.price}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost Price
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.cost_price}
                onChange={(e) => handleInputChange('cost_price', parseFloat(e.target.value) || 0)}
                className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
              />
            </div>
          </div>
        </div>

        {/* Additional Pricing */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Retail Price (MSRP)
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.retail_price}
                onChange={(e) => handleInputChange('retail_price', parseFloat(e.target.value) || 0)}
                className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">Manufacturer's suggested retail price</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sale Price
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.sale_price}
                onChange={(e) => handleInputChange('sale_price', parseFloat(e.target.value) || 0)}
                className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">Leave blank if not on sale</p>
          </div>
        </div>

        {/* Tax Settings */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Tax Settings</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tax Class
              </label>
              <CustomSelect
                options={[
                  { value: 'default', label: 'Default Tax Class' },
                  { value: 'non-taxable', label: 'Non-Taxable' },
                  { value: 'shipping-only', label: 'Shipping Only' },
                  { value: 'reduced', label: 'Reduced Rate' }
                ]}
                value={formData.tax_class_id}
                onChange={(value) => handleInputChange('tax_class_id', value)}
                placeholder="Select tax class"
              />
            </div>

            <div className="flex items-center pt-8">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.price_includes_tax}
                  onChange={(e) => handleInputChange('price_includes_tax', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Price includes tax</span>
              </label>
            </div>
          </div>
        </div>

        {/* Bulk Pricing */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Bulk Pricing</h4>

          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
            <DollarSign className="h-8 w-8 text-gray-400 mx-auto mb-3" />
            <h4 className="text-md font-medium text-gray-900 mb-2">Add Bulk Pricing Rules</h4>
            <p className="text-gray-600 mb-4">Offer discounts for quantity purchases</p>
            <button
              type="button"
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Add Bulk Pricing Rule
            </button>
          </div>
        </div>
      </div>
    )
  }

  const renderInventorySection = () => {
    return (
      <div className="space-y-6">
        {/* Inventory Tracking */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Inventory Tracking
          </label>
          <CustomSelect
            options={[
              { value: 'none', label: 'Don\'t track inventory' },
              { value: 'simple', label: 'Track inventory' },
              { value: 'variant', label: 'Track inventory by variant' }
            ]}
            value={formData.inventory_tracking}
            onChange={(value) => handleInputChange('inventory_tracking', value)}
            placeholder="Select inventory tracking"
          />
        </div>

        {/* Current Stock Level */}
        {formData.inventory_tracking !== 'none' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current Stock Level
              </label>
              <input
                type="number"
                min="0"
                value={formData.inventory_level}
                onChange={(e) => handleInputChange('inventory_level', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Low Stock Warning Level
              </label>
              <input
                type="number"
                min="0"
                value={formData.inventory_warning_level}
                onChange={(e) => handleInputChange('inventory_warning_level', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
              />
            </div>
          </div>
        )}

        {/* Order Quantity Limits */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Order Quantity Limits</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Order Quantity
              </label>
              <input
                type="number"
                min="1"
                value={formData.order_quantity_minimum}
                onChange={(e) => handleInputChange('order_quantity_minimum', parseInt(e.target.value) || 1)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Order Quantity
              </label>
              <input
                type="number"
                min="0"
                value={formData.order_quantity_maximum}
                onChange={(e) => handleInputChange('order_quantity_maximum', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0 (unlimited)"
              />
              <p className="mt-1 text-xs text-gray-500">Set to 0 for unlimited quantity</p>
            </div>
          </div>
        </div>

        {/* Bin Picking Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Bin Picking Number
          </label>
          <input
            type="text"
            value={formData.bin_picking_number}
            onChange={(e) => handleInputChange('bin_picking_number', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter bin picking number"
          />
          <p className="mt-1 text-xs text-gray-500">Used for warehouse management and order fulfillment</p>
        </div>
      </div>
    )
  }

  const renderShippingSection = () => {
    return (
      <div className="space-y-6">
        {/* Physical Product Dimensions */}
        {formData.product_type === 'physical' && (
          <>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Weight (lbs)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.weight}
                  onChange={(e) => handleInputChange('weight', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Width (in)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.width}
                  onChange={(e) => handleInputChange('width', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Height (in)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.height}
                  onChange={(e) => handleInputChange('height', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Depth (in)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.depth}
                  onChange={(e) => handleInputChange('depth', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
              </div>
            </div>

            {/* Shipping Options */}
            <div className="border-t pt-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Shipping Options</h4>

              <div className="space-y-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_free_shipping}
                    onChange={(e) => handleInputChange('is_free_shipping', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <Truck className="h-4 w-4 ml-2 mr-1 text-gray-500" />
                  <span className="text-sm text-gray-700">Free shipping</span>
                </label>

                {!formData.is_free_shipping && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Fixed Shipping Cost
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">$</span>
                      </div>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.fixed_cost_shipping_price}
                        onChange={(e) => handleInputChange('fixed_cost_shipping_price', parseFloat(e.target.value) || 0)}
                        className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="0.00"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">Leave blank to use calculated shipping</p>
                  </div>
                )}
              </div>
            </div>

            {/* Gift Wrapping */}
            <div className="border-t pt-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Gift Wrapping</h4>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gift Wrapping Options
                </label>
                <CustomSelect
                  options={[
                    { value: 'any', label: 'Any gift wrapping options' },
                    { value: 'none', label: 'No gift wrapping' },
                    { value: 'list', label: 'Specific gift wrapping options' }
                  ]}
                  value={formData.gift_wrapping_options_type}
                  onChange={(value) => handleInputChange('gift_wrapping_options_type', value)}
                  placeholder="Select gift wrapping options"
                />
              </div>
            </div>
          </>
        )}

        {/* Warranty */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Warranty Information
          </label>
          <textarea
            rows={3}
            value={formData.warranty}
            onChange={(e) => handleInputChange('warranty', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter warranty information"
          />
        </div>
      </div>
    )
  }

  const renderSeoSection = () => {
    return (
      <div className="space-y-6">
        {/* Page Title & Meta */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Page Title
          </label>
          <input
            type="text"
            value={formData.page_title}
            onChange={(e) => handleInputChange('page_title', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter page title (leave blank to use product name)"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Meta Description
          </label>
          <textarea
            rows={3}
            value={formData.meta_description}
            onChange={(e) => handleInputChange('meta_description', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter meta description for search engines"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Search Keywords
          </label>
          <input
            type="text"
            value={formData.search_keywords}
            onChange={(e) => handleInputChange('search_keywords', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter search keywords (comma separated)"
          />
        </div>

        {/* Product Identifiers */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Product Identifiers</h4>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                UPC
              </label>
              <input
                type="text"
                value={formData.upc}
                onChange={(e) => handleInputChange('upc', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter UPC"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                MPN
              </label>
              <input
                type="text"
                value={formData.mpn}
                onChange={(e) => handleInputChange('mpn', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter MPN"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                GTIN
              </label>
              <input
                type="text"
                value={formData.gtin}
                onChange={(e) => handleInputChange('gtin', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter GTIN"
              />
            </div>
          </div>
        </div>

        {/* Open Graph */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Open Graph (Social Media)</h4>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Open Graph Type
            </label>
            <CustomSelect
              options={[
                { value: 'product', label: 'Product' },
                { value: 'article', label: 'Article' },
                { value: 'website', label: 'Website' }
              ]}
              value={formData.open_graph_type}
              onChange={(value) => handleInputChange('open_graph_type', value)}
              placeholder="Select Open Graph type"
            />
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Open Graph Title
            </label>
            <input
              type="text"
              value={formData.open_graph_title}
              onChange={(e) => handleInputChange('open_graph_title', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter Open Graph title"
            />
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Open Graph Description
            </label>
            <textarea
              rows={3}
              value={formData.open_graph_description}
              onChange={(e) => handleInputChange('open_graph_description', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter Open Graph description"
            />
          </div>

          <div className="mt-4 space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.open_graph_use_meta_description}
                onChange={(e) => handleInputChange('open_graph_use_meta_description', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Use meta description for Open Graph</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.open_graph_use_product_name}
                onChange={(e) => handleInputChange('open_graph_use_product_name', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Use product name for Open Graph title</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.open_graph_use_image}
                onChange={(e) => handleInputChange('open_graph_use_image', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Use product image for Open Graph</span>
            </label>
          </div>
        </div>
      </div>
    )
  }

  const renderImagesSection = () => {
    return (
      <div className="space-y-6">
        {/* Product Images */}
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
          <Image className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Add Product Images</h3>
          <p className="text-gray-600 mb-4">
            Upload images to showcase your product. First image will be the main product image.
          </p>
          <div className="flex justify-center space-x-3">
            <button
              type="button"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Upload Images
            </button>
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              Add from URL
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-3">
            Supported formats: JPG, PNG, GIF, WebP. Max size: 10MB per image.
          </p>
        </div>

        {/* Image Alt Text */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Image Alt Text
          </label>
          <input
            type="text"
            value={formData.image_alt_text}
            onChange={(e) => handleInputChange('image_alt_text', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Describe the image for accessibility and SEO"
          />
          <p className="mt-1 text-xs text-gray-500">Used for screen readers and when images fail to load</p>
        </div>

        {/* Product Videos */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Product Videos</h4>
          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
            <div className="flex justify-center mb-3">
              <div className="bg-gray-100 p-3 rounded-full">
                <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
                </svg>
              </div>
            </div>
            <h4 className="text-md font-medium text-gray-900 mb-2">Add Product Videos</h4>
            <p className="text-gray-600 mb-4">Add YouTube or Vimeo videos to showcase your product</p>
            <button
              type="button"
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Add Video URL
            </button>
          </div>
        </div>

        {/* Video URL Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Video URL
          </label>
          <input
            type="url"
            value={formData.video_url}
            onChange={(e) => handleInputChange('video_url', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="https://www.youtube.com/watch?v=... or https://vimeo.com/..."
          />
          <p className="mt-1 text-xs text-gray-500">Supports YouTube and Vimeo URLs</p>
        </div>
      </div>
    )
  }

  const renderVariantsSection = () => {
    return (
      <div className="space-y-6">
        {/* Variant Options */}
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
          <Palette className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Product Variants</h3>
          <p className="text-gray-600 mb-4">
            Create variants for different sizes, colors, materials, or other options
          </p>
          <button
            type="button"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Add Variant Option
          </button>
        </div>

        {/* Variant Configuration */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Variant Configuration</h4>

          <div className="space-y-4">
            {/* Option Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Option Name
              </label>
              <input
                type="text"
                value={formData.variant_option_name}
                onChange={(e) => handleInputChange('variant_option_name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Size, Color, Material"
              />
            </div>

            {/* Option Values */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Option Values
              </label>
              <input
                type="text"
                value={formData.variant_option_values}
                onChange={(e) => handleInputChange('variant_option_values', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Small, Medium, Large (comma separated)"
              />
              <p className="mt-1 text-xs text-gray-500">Separate multiple values with commas</p>
            </div>

            {/* Variant Display Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Display Type
              </label>
              <CustomSelect
                options={[
                  { value: 'dropdown', label: 'Dropdown List' },
                  { value: 'radio', label: 'Radio Buttons' },
                  { value: 'swatch', label: 'Color/Image Swatches' },
                  { value: 'rectangle', label: 'Rectangle List' }
                ]}
                value={formData.variant_display_type}
                onChange={(value) => handleInputChange('variant_display_type', value)}
                placeholder="Select display type"
              />
            </div>
          </div>
        </div>

        {/* Variant Pricing */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Variant Pricing</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Modifier Type
              </label>
              <CustomSelect
                options={[
                  { value: 'fixed', label: 'Fixed Amount' },
                  { value: 'percentage', label: 'Percentage' }
                ]}
                value={formData.variant_price_modifier_type}
                onChange={(value) => handleInputChange('variant_price_modifier_type', value)}
                placeholder="Select modifier type"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Modifier
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">
                    {formData.variant_price_modifier_type === 'percentage' ? '%' : '$'}
                  </span>
                </div>
                <input
                  type="number"
                  step="0.01"
                  value={formData.variant_price_modifier}
                  onChange={(e) => handleInputChange('variant_price_modifier', parseFloat(e.target.value) || 0)}
                  className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Variant Inventory */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Variant Inventory</h4>

          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.variant_track_inventory}
                onChange={(e) => handleInputChange('variant_track_inventory', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Track inventory for each variant separately</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.variant_required}
                onChange={(e) => handleInputChange('variant_required', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Customer must select an option</span>
            </label>
          </div>
        </div>
      </div>
    )
  }

  const renderAdvancedSection = () => {
    return (
      <div className="space-y-6">
        {/* Product Availability */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4">Product Availability</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Availability
              </label>
              <CustomSelect
                options={[
                  { value: 'available', label: 'Available' },
                  { value: 'disabled', label: 'Disabled' },
                  { value: 'preorder', label: 'Preorder' }
                ]}
                value={formData.availability}
                onChange={(value) => handleInputChange('availability', value)}
                placeholder="Select availability"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Availability Description
              </label>
              <input
                type="text"
                value={formData.availability_description}
                onChange={(e) => handleInputChange('availability_description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Usually ships in 24 hours"
              />
            </div>
          </div>
        </div>

        {/* Preorder Settings */}
        {formData.availability === 'preorder' && (
          <div className="border-t pt-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Preorder Settings</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Release Date
                </label>
                <input
                  type="date"
                  value={formData.preorder_release_date}
                  onChange={(e) => handleInputChange('preorder_release_date', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preorder Message
                </label>
                <input
                  type="text"
                  value={formData.preorder_message}
                  onChange={(e) => handleInputChange('preorder_message', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Expected to ship on..."
                />
              </div>
            </div>
          </div>
        )}

        {/* Purchase Options */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Purchase Options</h4>

          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_price_hidden}
                onChange={(e) => handleInputChange('is_price_hidden', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Hide price from customers</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_preorder_only}
                onChange={(e) => handleInputChange('is_preorder_only', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Preorder only</span>
            </label>
          </div>
        </div>

        {/* Custom Fields */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Custom Fields</h4>

          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
            <Settings className="h-8 w-8 text-gray-400 mx-auto mb-3" />
            <h4 className="text-md font-medium text-gray-900 mb-2">Add Custom Fields</h4>
            <p className="text-gray-600 mb-4">Create custom fields to store additional product information</p>
            <button
              type="button"
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Add Custom Field
            </button>
          </div>
        </div>

        {/* Related Products */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Related Products</h4>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Related Product IDs
            </label>
            <input
              type="text"
              value={formData.related_products}
              onChange={(e) => handleInputChange('related_products', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter product IDs (comma separated)"
            />
            <p className="mt-1 text-xs text-gray-500">Products that will be shown as related or recommended</p>
          </div>
        </div>

        {/* Sort Order */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Display Settings</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sort Order
              </label>
              <input
                type="number"
                value={formData.sort_order}
                onChange={(e) => handleInputChange('sort_order', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
              />
              <p className="mt-1 text-xs text-gray-500">Lower numbers appear first in product listings</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Layout File
              </label>
              <input
                type="text"
                value={formData.layout_file}
                onChange={(e) => handleInputChange('layout_file', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="product.html"
              />
              <p className="mt-1 text-xs text-gray-500">Custom template file for this product</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Add scroll functionality
  const sectionRefs = useRef<Record<string, HTMLElement | null>>({})

  const scrollToSection = (sectionId: string) => {
    setActiveTab(sectionId)
    const element = sectionRefs.current[sectionId]
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  // Track active section on scroll
  useEffect(() => {
    const handleScroll = () => {
      const sections = Object.keys(sectionRefs.current)
      const scrollPosition = window.scrollY + 200

      for (const sectionId of sections) {
        const element = sectionRefs.current[sectionId]
        if (element) {
          const { offsetTop, offsetHeight } = element
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveTab(sectionId)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Show loading state when fetching product data in edit mode
  if (isEditMode && isLoadingProduct) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading product data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="-m-6 min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/products')}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Package className="h-7 w-7 text-blue-600 mr-3" />
                  {isEditMode ? 'Edit Product' : 'Create Product'}
                </h1>
                <p className="text-gray-600 mt-1">
                  {isEditMode ? 'Update product information' : 'Add a new product to your catalog'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={() => navigate('/products')}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={saveProductMutation.isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {saveProductMutation.isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {isEditMode ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {isEditMode ? 'Update Product' : 'Save Product'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content Layout */}
      <div className="flex">
        {/* Sticky Left Navigation */}
        <div className="w-80 bg-white border-r border-gray-200 sticky top-0 h-screen overflow-y-auto">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Sections</h3>
            <nav className="space-y-2">
              {tabs.map((tab, index) => {
                const Icon = tab.icon
                const isActive = activeTab === tab.id
                const isCompleted = tabs.findIndex(t => t.id === activeTab) > index

                return (
                  <button
                    key={tab.id}
                    onClick={() => scrollToSection(tab.id)}
                    className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-500'
                        : isCompleted
                        ? 'bg-green-50 text-green-700 hover:bg-green-100'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className={`h-5 w-5 mr-3 flex-shrink-0 ${
                      isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                    }`} />
                    <div className="flex-1">
                      <div className="font-medium">{tab.label}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {tab.id === 'basic' && 'Name, description, category'}
                        {tab.id === 'pricing' && 'Price, cost, tax settings'}
                        {tab.id === 'inventory' && 'Stock levels, tracking'}
                        {tab.id === 'shipping' && 'Weight, dimensions'}
                        {tab.id === 'seo' && 'Meta tags, URLs'}
                        {tab.id === 'images' && 'Photos, videos'}
                        {tab.id === 'variants' && 'Options, variations'}
                        {tab.id === 'advanced' && 'Custom fields, settings'}
                      </div>
                    </div>
                    {isCompleted && (
                      <div className="h-2 w-2 bg-green-500 rounded-full flex-shrink-0"></div>
                    )}
                    {isActive && (
                      <div className="h-2 w-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                    )}
                  </button>
                )
              })}
            </nav>

            {/* Progress Indicator */}
            <div className="mt-8 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Progress</span>
                <span>{Math.round((tabs.findIndex(t => t.id === activeTab) + 1) / tabs.length * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(tabs.findIndex(t => t.id === activeTab) + 1) / tabs.length * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Content - Continuous Scroll */}
        <div className="flex-1 min-h-screen bg-gray-50">
          <div className="max-w-4xl mx-auto px-6 py-8">
            <form onSubmit={handleSubmit} className="space-y-12">
              {/* Basic Information Section */}
              <section
                ref={(el) => sectionRefs.current['basic'] = el}
                id="basic"
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
              >
                <div className="mb-8">
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                    <Package className="h-5 w-5 text-blue-600 mr-2" />
                    Basic Information
                  </h2>
                  <p className="text-gray-600 mt-1">Essential product details and categorization</p>
                </div>
                {renderBasicSection()}
              </section>

              {/* Pricing Section */}
              <section
                ref={(el) => sectionRefs.current['pricing'] = el}
                id="pricing"
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
              >
                <div className="mb-8">
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                    <DollarSign className="h-5 w-5 text-blue-600 mr-2" />
                    Pricing
                  </h2>
                  <p className="text-gray-600 mt-1">Set pricing, costs, and display options</p>
                </div>
                {renderPricingSection()}
              </section>

              {/* Inventory Section */}
              <section
                ref={(el) => sectionRefs.current['inventory'] = el}
                id="inventory"
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
              >
                <div className="mb-8">
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                    <BarChart3 className="h-5 w-5 text-blue-600 mr-2" />
                    Inventory
                  </h2>
                  <p className="text-gray-600 mt-1">Manage stock levels and availability</p>
                </div>
                {renderInventorySection()}
              </section>

              {/* Shipping Section */}
              <section
                ref={(el) => sectionRefs.current['shipping'] = el}
                id="shipping"
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
              >
                <div className="mb-8">
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                    <Truck className="h-5 w-5 text-blue-600 mr-2" />
                    Shipping
                  </h2>
                  <p className="text-gray-600 mt-1">Configure shipping options and dimensions</p>
                </div>
                {renderShippingSection()}
              </section>

              {/* SEO Section */}
              <section
                ref={(el) => sectionRefs.current['seo'] = el}
                id="seo"
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
              >
                <div className="mb-8">
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                    <Search className="h-5 w-5 text-blue-600 mr-2" />
                    SEO & URLs
                  </h2>
                  <p className="text-gray-600 mt-1">Optimize for search engines and social media</p>
                </div>
                {renderSeoSection()}
              </section>

              {/* Images Section */}
              <section
                ref={(el) => sectionRefs.current['images'] = el}
                id="images"
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
              >
                <div className="mb-8">
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                    <Image className="h-5 w-5 text-blue-600 mr-2" />
                    Images & Videos
                  </h2>
                  <p className="text-gray-600 mt-1">Upload product media and galleries</p>
                </div>
                {renderImagesSection()}
              </section>

              {/* Variants Section */}
              <section
                ref={(el) => sectionRefs.current['variants'] = el}
                id="variants"
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
              >
                <div className="mb-8">
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                    <Palette className="h-5 w-5 text-blue-600 mr-2" />
                    Variants
                  </h2>
                  <p className="text-gray-600 mt-1">Create product options and variations</p>
                </div>
                {renderVariantsSection()}
              </section>

              {/* Advanced Section */}
              <section
                ref={(el) => sectionRefs.current['advanced'] = el}
                id="advanced"
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
              >
                <div className="mb-8">
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                    <Settings className="h-5 w-5 text-blue-600 mr-2" />
                    Advanced
                  </h2>
                  <p className="text-gray-600 mt-1">Additional settings and configurations</p>
                </div>
                {renderAdvancedSection()}
              </section>
            </form>
          </div>
        </div>
      </div>

      {/* Floating Save Button */}
      <div className="fixed bottom-6 right-6 z-40">
        <button
          onClick={handleSubmit}
          disabled={saveProductMutation.isLoading}
          className="px-6 py-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200 hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          {saveProductMutation.isLoading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              {isEditMode ? 'Updating...' : 'Saving...'}
            </>
          ) : (
            <>
              <Save className="h-5 w-5 mr-2" />
              {isEditMode ? 'Update Product' : 'Save Product'}
            </>
          )}
        </button>
      </div>
    </div>
  )
}

export default CreateProductPage
