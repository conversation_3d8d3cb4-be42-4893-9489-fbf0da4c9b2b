import { useState, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import {
  Package,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  ChevronDown,
  X,
  Check,
  Settings,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Calendar,
  DollarSign,
  BarChart3,
  Tag,
  Star,
  Truck,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react'
import { mockProductApi, mockCategories } from '../services/productApi'
import { ProductListItem, ProductQueryParams } from '../types/product'
import CustomSelect from '../components/CustomSelect'
import toast from 'react-hot-toast'

const ProductsPage = () => {
  const navigate = useNavigate()

  // View and layout state
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table')
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnSettings, setShowColumnSettings] = useState(false)

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState({
    category: '',
    is_visible: '',
    is_featured: '',
    sync_status: '',
    price_range: { min: '', max: '' },
    inventory_range: { min: '', max: '' },
    date_range: { start: '', end: '' }
  })

  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(20)
  const [sortBy, setSortBy] = useState('updated_at')
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC')

  // Column visibility
  const [visibleColumns, setVisibleColumns] = useState({
    image: true,
    name: true,
    sku: true,
    category: true,
    price: true,
    inventory: true,
    status: true,
    sync_status: true,
    updated_at: true,
    actions: true
  })

  // Mock data for testing
  const mockProducts = [
    {
      id: '1',
      name: 'Wireless Bluetooth Headphones',
      sku: 'WBH-001',
      price: 99.99,
      inventory_level: 45,
      is_visible: true,
      is_featured: true,
      sync_status: 'synced',
      updated_at: '2023-06-22T14:30:00Z'
    },
    {
      id: '2',
      name: 'Smart Fitness Watch',
      sku: 'SFW-002',
      price: 199.99,
      inventory_level: 23,
      is_visible: true,
      is_featured: false,
      sync_status: 'pending',
      updated_at: '2023-06-22T12:15:00Z'
    },
    {
      id: '3',
      name: 'Organic Cotton T-Shirt',
      sku: 'OCT-003',
      price: 29.99,
      inventory_level: 0,
      is_visible: false,
      is_featured: false,
      sync_status: 'error',
      updated_at: '2023-06-21T16:45:00Z'
    }
  ]

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getSyncStatusBadge = (status: string) => {
    const statusConfig = {
      synced: { color: 'bg-green-100 text-green-800', label: 'Synced' },
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      error: { color: 'bg-red-100 text-red-800', label: 'Error' },
      not_synced: { color: 'bg-gray-100 text-gray-800', label: 'Not Synced' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.not_synced

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Package className="h-8 w-8 text-blue-600 mr-3" />
            Products
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your product catalog and sync with BigCommerce
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={() => navigate('/products/create')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2 inline" />
            Create Product
          </button>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  SKU
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Inventory
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sync Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Updated
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="ml-3 text-gray-600">Loading products...</span>
                    </div>
                  </td>
                </tr>
              ) : mockProducts.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                    <p className="text-gray-600 mb-4">
                      Get started by creating your first product
                    </p>
                    <button
                      onClick={() => navigate('/products/create')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Plus className="h-4 w-4 mr-2 inline" />
                      Create Product
                    </button>
                  </td>
                </tr>
              ) : (
                mockProducts.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                            <Package className="h-5 w-5 text-gray-500" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {product.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {product.is_visible ? 'Visible' : 'Hidden'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {product.sku || '-'}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {formatPrice(product.price)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        product.inventory_level > 10
                          ? 'bg-green-100 text-green-800'
                          : product.inventory_level > 0
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {product.inventory_level} in stock
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      {getSyncStatusBadge(product.sync_status)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {formatDate(product.updated_at)}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default ProductsPage
