import React, { useState, useMemo } from 'react'
import { useQuery } from 'react-query'
import { useNavigate } from 'react-router-dom'
import {
  Search,
  Filter,
  Download,
  Eye,
  MoreHorizontal,
  ChevronDown,
  Calendar,
  DollarSign,
  Package,
  User,
  CreditCard,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  RefreshCw
} from 'lucide-react'
import { mockOrderApi } from '../services/orderApi'
import { OrderQueryParams } from '../types/order'
import CustomSelect from '../components/CustomSelect'

const OrdersPage = () => {
  const navigate = useNavigate()
  
  // State for filters and search
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(20)
  const [sortBy, setSortBy] = useState('date_created')
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC')

  // Visible columns state
  const [visibleColumns, setVisibleColumns] = useState({
    order_id: true,
    customer: true,
    date_created: true,
    status: true,
    payment_status: true,
    payment_method: true,
    total: true,
    items: true,
    shipping_address: true,
    actions: true
  })

  // Query parameters
  const queryParams: OrderQueryParams = useMemo(() => ({
    page: currentPage,
    limit: itemsPerPage,
    search: searchTerm || undefined,
    status: selectedStatus || undefined,
    payment_status: selectedPaymentStatus || undefined,
    sort_by: sortBy,
    sort_order: sortOrder
  }), [currentPage, itemsPerPage, searchTerm, selectedStatus, selectedPaymentStatus, sortBy, sortOrder])

  // Fetch orders
  const { data: ordersData, isLoading, error } = useQuery(
    ['orders', queryParams],
    () => mockOrderApi.getOrders(queryParams),
    {
      keepPreviousData: true,
      staleTime: 30000
    }
  )

  // Status options
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'Pending', label: 'Pending' },
    { value: 'Processing', label: 'Processing' },
    { value: 'Shipped', label: 'Shipped' },
    { value: 'Completed', label: 'Completed' },
    { value: 'Cancelled', label: 'Cancelled' },
    { value: 'Refunded', label: 'Refunded' }
  ]

  const paymentStatusOptions = [
    { value: '', label: 'All Payment Statuses' },
    { value: 'Pending', label: 'Pending' },
    { value: 'Captured', label: 'Captured' },
    { value: 'Partially Captured', label: 'Partially Captured' },
    { value: 'Authorized', label: 'Authorized' },
    { value: 'Partially Refunded', label: 'Partially Refunded' },
    { value: 'Refunded', label: 'Refunded' },
    { value: 'Voided', label: 'Voided' }
  ]

  // Helper functions
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Pending': { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: Clock },
      'Processing': { bg: 'bg-blue-100', text: 'text-blue-800', icon: RefreshCw },
      'Shipped': { bg: 'bg-purple-100', text: 'text-purple-800', icon: Truck },
      'Completed': { bg: 'bg-green-100', text: 'text-green-800', icon: CheckCircle },
      'Cancelled': { bg: 'bg-red-100', text: 'text-red-800', icon: XCircle },
      'Refunded': { bg: 'bg-gray-100', text: 'text-gray-800', icon: RefreshCw }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['Pending']
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        <Icon className="h-3 w-3 mr-1" />
        {status}
      </span>
    )
  }

  const getPaymentStatusBadge = (paymentStatus: string) => {
    const statusConfig = {
      'Pending': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
      'Captured': { bg: 'bg-green-100', text: 'text-green-800' },
      'Partially Captured': { bg: 'bg-blue-100', text: 'text-blue-800' },
      'Authorized': { bg: 'bg-purple-100', text: 'text-purple-800' },
      'Partially Refunded': { bg: 'bg-orange-100', text: 'text-orange-800' },
      'Refunded': { bg: 'bg-red-100', text: 'text-red-800' },
      'Voided': { bg: 'bg-gray-100', text: 'text-gray-800' }
    }

    const config = statusConfig[paymentStatus as keyof typeof statusConfig] || statusConfig['Pending']

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {paymentStatus}
      </span>
    )
  }

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'ASC' ? 'DESC' : 'ASC')
    } else {
      setSortBy(column)
      setSortOrder('DESC')
    }
  }

  const handleOrderClick = (orderId: string) => {
    // Open order details in new window
    window.open(`/orders/${orderId}`, '_blank')
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Orders</h2>
          <p className="text-gray-600">Please try again later.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">All Orders</h1>
          <p className="text-gray-600 mt-1">
            {ordersData ? `${ordersData.total} total orders` : 'Loading orders...'}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors flex items-center">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search orders, customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Status Filter */}
          <CustomSelect
            options={statusOptions}
            value={selectedStatus}
            onChange={setSelectedStatus}
            placeholder="Filter by status"
          />

          {/* Payment Status Filter */}
          <CustomSelect
            options={paymentStatusOptions}
            value={selectedPaymentStatus}
            onChange={setSelectedPaymentStatus}
            placeholder="Filter by payment status"
          />

          {/* Items per page */}
          <CustomSelect
            options={[
              { value: '10', label: 'Show 10' },
              { value: '20', label: 'Show 20' },
              { value: '50', label: 'Show 50' },
              { value: '100', label: 'Show 100' }
            ]}
            value={itemsPerPage.toString()}
            onChange={(value) => setItemsPerPage(parseInt(value))}
            placeholder="Items per page"
          />
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full table-auto divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Order ID */}
                {visibleColumns.order_id && (
                  <th 
                    className="w-32 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('id')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Order ID</span>
                      <ChevronDown className="h-3 w-3" />
                    </div>
                  </th>
                )}

                {/* Customer */}
                {visibleColumns.customer && (
                  <th 
                    className="min-w-0 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('customer_name')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Customer</span>
                      <ChevronDown className="h-3 w-3" />
                    </div>
                  </th>
                )}

                {/* Date Created */}
                {visibleColumns.date_created && (
                  <th 
                    className="w-36 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('date_created')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Date</span>
                      <ChevronDown className="h-3 w-3" />
                    </div>
                  </th>
                )}

                {/* Status */}
                {visibleColumns.status && (
                  <th className="w-32 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                )}

                {/* Payment Status */}
                {visibleColumns.payment_status && (
                  <th className="w-36 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment
                  </th>
                )}

                {/* Payment Method */}
                {visibleColumns.payment_method && (
                  <th className="w-32 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                )}

                {/* Total */}
                {visibleColumns.total && (
                  <th 
                    className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('total_inc_tax')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Total</span>
                      <ChevronDown className="h-3 w-3" />
                    </div>
                  </th>
                )}

                {/* Items */}
                {visibleColumns.items && (
                  <th className="w-16 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Items
                  </th>
                )}

                {/* Shipping Address */}
                {visibleColumns.shipping_address && (
                  <th className="w-40 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ship To
                  </th>
                )}

                {/* Actions */}
                {visibleColumns.actions && (
                  <th className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                )}
              </tr>
            </thead>

            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                // Loading skeleton
                Array.from({ length: 5 }).map((_, index) => (
                  <tr key={index}>
                    <td colSpan={Object.values(visibleColumns).filter(Boolean).length} className="px-4 py-4">
                      <div className="animate-pulse flex space-x-4">
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                ))
              ) : ordersData?.orders.length === 0 ? (
                <tr>
                  <td colSpan={Object.values(visibleColumns).filter(Boolean).length} className="px-4 py-12 text-center">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
                    <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
                  </td>
                </tr>
              ) : (
                ordersData?.orders.map((order) => (
                  <tr 
                    key={order.id} 
                    className="hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => handleOrderClick(order.id)}
                  >
                    {/* Order ID */}
                    {visibleColumns.order_id && (
                      <td className="w-32 px-4 py-4 text-sm font-medium text-blue-600 hover:text-blue-800">
                        {order.id}
                      </td>
                    )}

                    {/* Customer */}
                    {visibleColumns.customer && (
                      <td className="min-w-0 px-4 py-4">
                        <div className="min-w-0">
                          <div className="text-sm font-medium text-gray-900 truncate">{order.customer_name}</div>
                          <div className="text-xs text-gray-500 truncate">{order.customer_email}</div>
                        </div>
                      </td>
                    )}

                    {/* Date Created */}
                    {visibleColumns.date_created && (
                      <td className="w-36 px-4 py-4 text-xs text-gray-900">
                        {formatDate(order.date_created)}
                      </td>
                    )}

                    {/* Status */}
                    {visibleColumns.status && (
                      <td className="w-32 px-4 py-4">
                        {getStatusBadge(order.status)}
                      </td>
                    )}

                    {/* Payment Status */}
                    {visibleColumns.payment_status && (
                      <td className="w-36 px-4 py-4">
                        {getPaymentStatusBadge(order.payment_status)}
                      </td>
                    )}

                    {/* Payment Method */}
                    {visibleColumns.payment_method && (
                      <td className="w-32 px-4 py-4 text-xs text-gray-900">
                        <div className="flex items-center">
                          <CreditCard className="h-3 w-3 mr-1 text-gray-400" />
                          {order.payment_method}
                        </div>
                      </td>
                    )}

                    {/* Total */}
                    {visibleColumns.total && (
                      <td className="w-24 px-4 py-4 text-xs text-gray-900 font-medium">
                        {formatPrice(order.total_inc_tax)}
                      </td>
                    )}

                    {/* Items */}
                    {visibleColumns.items && (
                      <td className="w-16 px-4 py-4 text-xs text-gray-900 text-center">
                        {order.items_total}
                      </td>
                    )}

                    {/* Shipping Address */}
                    {visibleColumns.shipping_address && (
                      <td className="w-40 px-4 py-4 text-xs text-gray-900">
                        <div className="truncate">
                          {order.shipping_addresses[0]?.city}, {order.shipping_addresses[0]?.state}
                        </div>
                      </td>
                    )}

                    {/* Actions */}
                    {visibleColumns.actions && (
                      <td className="w-24 px-4 py-4 text-sm font-medium">
                        <div className="flex items-center space-x-1">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleOrderClick(order.id)
                            }}
                            className="text-blue-600 hover:text-blue-900 transition-colors p-1"
                            title="View Order"
                          >
                            <Eye className="h-3 w-3" />
                          </button>
                          <button
                            onClick={(e) => e.stopPropagation()}
                            className="text-gray-600 hover:text-gray-900 transition-colors p-1"
                            title="More Actions"
                          >
                            <MoreHorizontal className="h-3 w-3" />
                          </button>
                        </div>
                      </td>
                    )}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {ordersData && ordersData.total_pages > 1 && (
          <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(ordersData.total_pages, currentPage + 1))}
                  disabled={currentPage === ordersData.total_pages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span>
                    {' '}to{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * itemsPerPage, ordersData.total)}
                    </span>
                    {' '}of{' '}
                    <span className="font-medium">{ordersData.total}</span>
                    {' '}results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    
                    {/* Page numbers */}
                    {Array.from({ length: Math.min(5, ordersData.total_pages) }, (_, i) => {
                      const pageNum = i + 1
                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === pageNum
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNum}
                        </button>
                      )
                    })}
                    
                    <button
                      onClick={() => setCurrentPage(Math.min(ordersData.total_pages, currentPage + 1))}
                      disabled={currentPage === ordersData.total_pages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default OrdersPage
