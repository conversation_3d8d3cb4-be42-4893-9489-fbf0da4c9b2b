import React, { useState } from 'react'
import {
  ArrowUpRight,
  Search,
  Filter,
  Download,
  Upload,
  Plus,
  Eye,
  Share2,
  Check,
  X,
  Clock,
  AlertCircle,
  Building2,
  Package,
  DollarSign,
  Star,
  MoreHorizontal,
  RefreshCw,
  Settings,
  Users,
  TrendingUp,
  BarChart3
} from 'lucide-react'
import StatCard from '../components/StatCard'

const OutboundSharingPage = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [showShareModal, setShowShareModal] = useState(false)

  // Mock data for your products available for sharing
  const mockSharedProducts = [
    {
      id: '1',
      name: 'Wireless Bluetooth Headphones',
      sku: 'WBH-001',
      category: 'Electronics',
      cost: 45.99,
      retail_price: 99.99,
      wholesale_price: 75.99,
      stock: 150,
      shared_with: ['TechCorp Solutions', 'ElectroMart'],
      sharing_status: 'active',
      total_shared_sales: 89,
      revenue_generated: 6757.11,
      last_shared: '2024-01-15T10:30:00Z',
      performance_rating: 4.8,
      vendor_requests: 3
    },
    {
      id: '2',
      name: 'Smart Fitness Watch',
      sku: 'SFW-002',
      category: 'Wearables',
      cost: 89.99,
      retail_price: 199.99,
      wholesale_price: 149.99,
      stock: 75,
      shared_with: ['HealthTech Inc'],
      sharing_status: 'pending',
      total_shared_sales: 23,
      revenue_generated: 3449.77,
      last_shared: '2024-01-14T15:45:00Z',
      performance_rating: 4.6,
      vendor_requests: 1
    },
    {
      id: '3',
      name: 'Organic Cotton T-Shirt',
      sku: 'OCT-003',
      category: 'Clothing',
      cost: 12.99,
      retail_price: 29.99,
      wholesale_price: 22.99,
      stock: 200,
      shared_with: [],
      sharing_status: 'not_shared',
      total_shared_sales: 0,
      revenue_generated: 0,
      last_shared: null,
      performance_rating: 0,
      vendor_requests: 5
    },
    {
      id: '4',
      name: 'Professional Camera Lens',
      sku: 'PCL-004',
      category: 'Photography',
      cost: 449.99,
      retail_price: 899.99,
      wholesale_price: 699.99,
      stock: 25,
      shared_with: ['PhotoGear Ltd', 'CameraPro'],
      sharing_status: 'active',
      total_shared_sales: 12,
      revenue_generated: 8399.88,
      last_shared: '2024-01-12T14:10:00Z',
      performance_rating: 4.9,
      vendor_requests: 0
    }
  ]

  // Mock data for Guest Organization distributors
  const mockDistributors = [
    {
      id: 'vendor_1',
      organization_name: 'TechCorp Solutions',
      contact_name: 'John Smith',
      user_type: 'distributor',
      status: 'active',
      price_list: 'Distributor Pricing'
    },
    {
      id: 'vendor_2',
      organization_name: 'HealthTech Inc',
      contact_name: 'Sarah Johnson',
      user_type: 'distributor',
      status: 'active',
      price_list: 'Standard Pricing'
    },
    {
      id: 'vendor_3',
      organization_name: 'EcoFashion Co',
      contact_name: 'Mike Green',
      user_type: 'distributor',
      status: 'pending',
      price_list: 'Wholesale Pricing'
    },
    {
      id: 'vendor_4',
      organization_name: 'PhotoGear Ltd',
      contact_name: 'Lisa Chen',
      user_type: 'distributor',
      status: 'active',
      price_list: 'Premium Pricing'
    }
  ]

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: Check, text: 'Active' },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, text: 'Pending' },
      not_shared: { color: 'bg-gray-100 text-gray-800', icon: X, text: 'Not Shared' },
      paused: { color: 'bg-red-100 text-red-800', icon: AlertCircle, text: 'Paused' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig]
    const Icon = config.icon
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const handleShareProduct = (productId: string) => {
    console.log('Sharing product:', productId)
    setShowShareModal(true)
  }

  const handleBulkShare = () => {
    console.log('Bulk sharing products:', selectedProducts)
    setShowShareModal(true)
  }

  const filteredProducts = mockSharedProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = !selectedCategory || product.category === selectedCategory
    const matchesStatus = !selectedStatus || product.sharing_status === selectedStatus
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <ArrowUpRight className="h-8 w-8 text-green-600 mr-3" />
            Outbound Products
          </h1>
          <p className="text-gray-600 mt-1">Manage products you share with vendor partners</p>
        </div>
        <div className="flex items-center space-x-3">
          {selectedProducts.length > 0 && (
            <button
              onClick={handleBulkShare}
              className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <Share2 className="h-4 w-4 mr-2 inline" />
              Share Selected ({selectedProducts.length})
            </button>
          )}
          <button className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2 inline" />
            Add Products to Share
          </button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title="Active Shares"
          value={mockSharedProducts.filter(p => p.sharing_status === 'active').length}
          subtitle="Products being shared"
          icon={Share2}
          iconColor="text-green-600"
          trend={{
            value: "8%",
            isPositive: true,
            period: "last month"
          }}
        />
        <StatCard
          title="Revenue Generated"
          value={`$${mockSharedProducts.reduce((sum, p) => sum + p.revenue_generated, 0).toLocaleString()}`}
          subtitle="From shared products"
          icon={DollarSign}
          iconColor="text-blue-600"
          trend={{
            value: "15.2%",
            isPositive: true,
            period: "last quarter"
          }}
        />
        <StatCard
          title="Total Sales"
          value={mockSharedProducts.reduce((sum, p) => sum + p.total_shared_sales, 0)}
          subtitle="Units sold"
          icon={TrendingUp}
          iconColor="text-purple-600"
          trend={{
            value: "23",
            isPositive: true,
            period: "last week"
          }}
        />
        <StatCard
          title="Vendor Requests"
          value={mockSharedProducts.reduce((sum, p) => sum + p.vendor_requests, 0)}
          subtitle="Pending requests"
          icon={Users}
          iconColor="text-orange-600"
          trend={{
            value: "3",
            isPositive: false,
            period: "last 7 days"
          }}
        />
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input pl-10 w-full"
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="">All Categories</option>
            <option value="Electronics">Electronics</option>
            <option value="Wearables">Wearables</option>
            <option value="Clothing">Clothing</option>
            <option value="Photography">Photography</option>
          </select>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="pending">Pending</option>
            <option value="not_shared">Not Shared</option>
            <option value="paused">Paused</option>
          </select>
          <div className="flex space-x-2">
            <button className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
            <button className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </button>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedProducts(filteredProducts.map(p => p.id))
                      } else {
                        setSelectedProducts([])
                      }
                    }}
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pricing</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shared With</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requests</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducts.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      checked={selectedProducts.includes(product.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedProducts([...selectedProducts, product.id])
                        } else {
                          setSelectedProducts(selectedProducts.filter(id => id !== product.id))
                        }
                      }}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                          <Package className="h-5 w-5 text-gray-500" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                        <div className="text-sm text-gray-500">{product.sku}</div>
                        <div className="text-xs text-gray-500">{product.category}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div>Cost: ${product.cost}</div>
                      <div>Wholesale: ${product.wholesale_price}</div>
                      <div className="text-xs text-gray-500">Retail: ${product.retail_price}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {product.shared_with.length > 0 ? (
                        <div>
                          {product.shared_with.slice(0, 2).map((vendor, index) => (
                            <div key={index} className="text-xs text-gray-600">{vendor}</div>
                          ))}
                          {product.shared_with.length > 2 && (
                            <div className="text-xs text-gray-500">+{product.shared_with.length - 2} more</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-500 text-sm">Not shared</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="flex items-center">
                        <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                        <span>{product.total_shared_sales} sales</span>
                      </div>
                      <div className="text-xs text-gray-500">${product.revenue_generated.toLocaleString()}</div>
                      {product.performance_rating > 0 && (
                        <div className="flex items-center">
                          <Star className="h-3 w-3 text-yellow-400 fill-current" />
                          <span className="text-xs text-gray-500 ml-1">{product.performance_rating}</span>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(product.sharing_status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {product.vendor_requests > 0 ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        {product.vendor_requests} pending
                      </span>
                    ) : (
                      <span className="text-gray-500 text-sm">None</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button className="text-gray-400 hover:text-gray-600">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-600">
                        <Settings className="h-4 w-4" />
                      </button>
                      {product.sharing_status === 'not_shared' && (
                        <button
                          onClick={() => handleShareProduct(product.id)}
                          className="text-green-600 hover:text-green-900"
                        >
                          <Share2 className="h-4 w-4" />
                        </button>
                      )}
                      <button className="text-gray-400 hover:text-gray-600">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Share Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
            <div className="flex justify-between items-start mb-6">
              <h3 className="text-lg font-medium text-gray-900">Share Products with Distributors</h3>
              <button
                onClick={() => setShowShareModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600">
                Select distributors from your Guest Organization to share {selectedProducts.length} product(s) with:
              </p>
            </div>

            <div className="space-y-3 max-h-64 overflow-y-auto">
              {mockDistributors
                .filter(distributor => distributor.status === 'active' && distributor.user_type === 'distributor')
                .map(distributor => (
                <label key={distributor.id} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <input type="checkbox" className="rounded border-gray-300 mr-3" />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{distributor.organization_name}</div>
                        <div className="text-xs text-gray-500">Contact: {distributor.contact_name}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-gray-500">Price List</div>
                        <div className="text-sm font-medium text-blue-600">{distributor.price_list}</div>
                      </div>
                    </div>
                  </div>
                </label>
              ))}
            </div>

            {mockDistributors.filter(d => d.status === 'active' && d.user_type === 'distributor').length === 0 && (
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No active distributors found in Guest Organization</p>
                <p className="text-sm text-gray-400 mt-1">
                  Add distributors in <a href="/settings/users/guest-organization" className="text-blue-600 hover:text-blue-800">Guest Organization settings</a>
                </p>
              </div>
            )}

            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setShowShareModal(false)}
                className="px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowShareModal(false)}
                className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
                disabled={mockDistributors.filter(d => d.status === 'active' && d.user_type === 'distributor').length === 0}
              >
                Share Products
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default OutboundSharingPage
