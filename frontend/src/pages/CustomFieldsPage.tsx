import { useState, useEffect } from 'react'
import {
  Search,
  Plus,
  ChevronDown,
  Edit,
  Copy,
  Trash2,
  Layers,
  Info,
  Filter,
  Download,
  Upload,
  Settings as SettingsIcon,
  Globe
} from 'lucide-react'
import CreateCustomFieldModal from '../components/CreateCustomFieldModal'
import WebhookConfigModal from '../components/WebhookConfigModal'
import CustomFieldSearch from '../components/CustomFieldSearch'
import toast from 'react-hot-toast'

interface CustomField {
  id: string
  label: string
  key: string
  field_type: string
  resource_type: string
  namespace: string
  description?: string
  is_required: boolean
  is_searchable: boolean
  is_filterable: boolean
  is_ai_enabled: boolean
  is_active: boolean
  created_at: string
  updated_at: string
}

interface CustomFieldsPageProps {}

const CustomFieldsPage: React.FC<CustomFieldsPageProps> = () => {
  const [fields, setFields] = useState<CustomField[]>([])
  const [loading, setLoading] = useState(true)
  const [searchFilters, setSearchFilters] = useState<any>({})
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showWebhookModal, setShowWebhookModal] = useState(false)
  const [editingField, setEditingField] = useState<CustomField | null>(null)
  const [webhookField, setWebhookField] = useState<CustomField | null>(null)

  // Sample data - replace with API call
  useEffect(() => {
    const sampleFields: CustomField[] = [
      {
        id: '1',
        label: 'SEO Title',
        key: 'seo_title',
        field_type: 'text',
        resource_type: 'products',
        namespace: 'global',
        description: 'Custom SEO title for search engines',
        is_required: false,
        is_searchable: true,
        is_filterable: false,
        is_ai_enabled: true,
        is_active: true,
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:30:00Z'
      },
      {
        id: '2',
        label: 'Warranty Period',
        key: 'warranty_period',
        field_type: 'number',
        resource_type: 'products',
        namespace: 'global',
        description: 'Warranty period in months',
        is_required: false,
        is_searchable: false,
        is_filterable: true,
        is_ai_enabled: false,
        is_active: true,
        created_at: '2024-01-14T09:15:00Z',
        updated_at: '2024-01-14T09:15:00Z'
      },
      {
        id: '3',
        label: 'Priority Level',
        key: 'priority_level',
        field_type: 'single_select',
        resource_type: 'customers',
        namespace: 'global',
        description: 'Customer priority level',
        is_required: false,
        is_searchable: false,
        is_filterable: true,
        is_ai_enabled: false,
        is_active: true,
        created_at: '2024-01-13T14:20:00Z',
        updated_at: '2024-01-13T14:20:00Z'
      }
    ]
    
    setTimeout(() => {
      setFields(sampleFields)
      setLoading(false)
    }, 500)
  }, [])

  const resourceTypes = [
    'products',
    'customers',
    'orders',
    'companies',
    'brands',
    'categories',
    'invoices',
    'quotes',
    'users',
    'pages',
    'global'
  ]

  const fieldTypes = [
    'text',
    'number',
    'single_select',
    'multi_select',
    'date',
    'boolean',
    'textarea',
    'url',
    'email'
  ]

  const namespaces = [
    'global',
    'form_specific'
  ]

  const getFieldTypeDisplay = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'text': 'Text',
      'number': 'Number',
      'single_select': 'Single Select',
      'multi_select': 'Multi Select',
      'date': 'Date',
      'boolean': 'Boolean',
      'textarea': 'Textarea',
      'url': 'URL',
      'email': 'Email'
    }
    return typeMap[type] || type
  }

  const getResourceTypeDisplay = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1)
  }

  const getFieldTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
      'text': 'bg-blue-100 text-blue-800',
      'number': 'bg-green-100 text-green-800',
      'single_select': 'bg-purple-100 text-purple-800',
      'multi_select': 'bg-purple-100 text-purple-800',
      'date': 'bg-orange-100 text-orange-800',
      'boolean': 'bg-gray-100 text-gray-800',
      'textarea': 'bg-blue-100 text-blue-800',
      'url': 'bg-indigo-100 text-indigo-800',
      'email': 'bg-pink-100 text-pink-800'
    }
    return colorMap[type] || 'bg-gray-100 text-gray-800'
  }

  const filteredFields = fields.filter(field => {
    // Search filter
    if (searchFilters.search) {
      const searchLower = searchFilters.search.toLowerCase()
      const matchesSearch = field.label.toLowerCase().includes(searchLower) ||
                           field.key.toLowerCase().includes(searchLower) ||
                           field.description?.toLowerCase().includes(searchLower)
      if (!matchesSearch) return false
    }

    // Resource type filter
    if (searchFilters.resourceType && searchFilters.resourceType !== field.resource_type) {
      return false
    }

    // Field type filter
    if (searchFilters.fieldType && searchFilters.fieldType !== field.field_type) {
      return false
    }

    // Namespace filter
    if (searchFilters.namespace && searchFilters.namespace !== field.namespace) {
      return false
    }

    // Boolean filters
    if (searchFilters.isRequired !== null && searchFilters.isRequired !== field.is_required) {
      return false
    }
    if (searchFilters.isSearchable !== null && searchFilters.isSearchable !== field.is_searchable) {
      return false
    }
    if (searchFilters.isFilterable !== null && searchFilters.isFilterable !== field.is_filterable) {
      return false
    }
    if (searchFilters.isAiEnabled !== null && searchFilters.isAiEnabled !== field.is_ai_enabled) {
      return false
    }
    if (searchFilters.isActive !== null && searchFilters.isActive !== field.is_active) {
      return false
    }

    // Date range filter
    if (searchFilters.dateRange?.from || searchFilters.dateRange?.to) {
      const fieldDate = new Date(field.created_at)
      if (searchFilters.dateRange.from && fieldDate < new Date(searchFilters.dateRange.from)) {
        return false
      }
      if (searchFilters.dateRange.to && fieldDate > new Date(searchFilters.dateRange.to)) {
        return false
      }
    }

    return true
  })

  const handleDeleteField = (fieldId: string) => {
    setFields(fields.filter(field => field.id !== fieldId))
    toast.success('Custom field deleted successfully')
  }

  const handleDuplicateField = (field: CustomField) => {
    const newField = {
      ...field,
      id: Date.now().toString(),
      label: `${field.label} (Copy)`,
      key: `${field.key}_copy`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    setFields([...fields, newField])
    toast.success('Custom field duplicated successfully')
  }

  const handleCreateField = () => {
    setEditingField(null)
    setShowCreateModal(true)
  }

  const handleEditField = (field: CustomField) => {
    setEditingField(field)
    setShowCreateModal(true)
  }

  const handleSaveField = (fieldData: CustomField) => {
    if (editingField) {
      // Update existing field
      setFields(fields.map(field =>
        field.id === editingField.id
          ? { ...fieldData, id: editingField.id, created_at: editingField.created_at, updated_at: new Date().toISOString() }
          : field
      ))
    } else {
      // Create new field
      const newField = {
        ...fieldData,
        id: Date.now().toString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      setFields([...fields, newField])
    }
    setShowCreateModal(false)
    setEditingField(null)
  }

  const handleConfigureWebhook = (field: CustomField) => {
    setWebhookField(field)
    setShowWebhookModal(true)
  }

  const handleSaveWebhookConfig = (integrations: any[]) => {
    // TODO: Save webhook configuration to backend
    console.log('Saving webhook config for field:', webhookField?.id, integrations)
    setShowWebhookModal(false)
    setWebhookField(null)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-start space-x-4">
          <div className="p-3 bg-blue-100 rounded-lg">
            <Layers className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Global Custom Fields</h1>
            <p className="text-gray-600 mt-1">
              Create reusable custom fields that can be activated in any form
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button className="btn btn-secondary flex items-center">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </button>
          <button className="btn btn-secondary flex items-center">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button
            onClick={handleCreateField}
            className="btn btn-primary flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Global Field
          </button>
        </div>
      </div>

      {/* Info Banner */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-blue-900">How Global Custom Fields Work</h3>
            <p className="text-sm text-blue-700 mt-1">
              Create fields here that can be activated in multiple forms. Each form will also have an option to create form-specific fields for unique use cases.
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <CustomFieldSearch
        onFiltersChange={setSearchFilters}
        resourceTypes={resourceTypes}
        fieldTypes={fieldTypes}
        namespaces={namespaces}
        totalResults={filteredFields.length}
      />

      {/* Fields Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Field
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Resource
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Features
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredFields.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center">
                      <Layers className="h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No custom fields found</h3>
                      <p className="text-gray-500 mb-4">
                        {Object.keys(searchFilters).some(key => searchFilters[key])
                          ? 'Try adjusting your search or filters'
                          : 'Get started by creating your first custom field'
                        }
                      </p>
                      {!Object.keys(searchFilters).some(key => searchFilters[key]) && (
                        <button
                          onClick={handleCreateField}
                          className="btn btn-primary flex items-center"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Create Global Field
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ) : (
                filteredFields.map((field) => (
                  <tr key={field.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Layers className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{field.label}</div>
                          <div className="text-sm text-gray-500">{field.key}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getFieldTypeColor(field.field_type)}`}>
                        {getFieldTypeDisplay(field.field_type)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{getResourceTypeDisplay(field.resource_type)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        field.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {field.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {field.is_searchable && (
                          <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            Searchable
                          </span>
                        )}
                        {field.is_ai_enabled && (
                          <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
                            AI
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEditField(field)}
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                          title="Edit"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleConfigureWebhook(field)}
                          className="text-purple-600 hover:text-purple-900 p-1 rounded hover:bg-purple-50"
                          title="Configure Webhooks"
                        >
                          <Globe className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDuplicateField(field)}
                          className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50"
                          title="Duplicate"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteField(field.id)}
                          className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                          title="Delete"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create/Edit Modal */}
      <CreateCustomFieldModal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false)
          setEditingField(null)
        }}
        onSave={handleSaveField}
        editingField={editingField}
      />

      {/* Webhook Configuration Modal */}
      <WebhookConfigModal
        isOpen={showWebhookModal}
        onClose={() => {
          setShowWebhookModal(false)
          setWebhookField(null)
        }}
        fieldId={webhookField?.id || ''}
        fieldLabel={webhookField?.label || ''}
        onSave={handleSaveWebhookConfig}
      />
    </div>
  )
}

export default CustomFieldsPage
