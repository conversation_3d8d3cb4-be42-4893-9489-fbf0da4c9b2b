@tailwind base;
@tailwind components;
@tailwind utilities;

/* Font Size Classes */
.text-small {
  font-size: 14px;
}

.text-medium {
  font-size: 16px;
}

.text-large {
  font-size: 18px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .dark {
    color-scheme: dark;
  }
}

/* Custom slider styles for color picker */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
  border: none;
  outline: none;
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider::-moz-range-track {
  height: 8px;
  border-radius: 4px;
  border: none;
  outline: none;
}

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-white text-gray-900;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  /* Custom Select/Dropdown Styles */
  .custom-select {
    @apply relative w-full;
  }

  .custom-select-trigger {
    @apply w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm cursor-pointer focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
  }

  .custom-select-trigger:hover {
    @apply border-gray-400;
  }

  .custom-select-trigger[aria-expanded="true"] {
    @apply border-blue-500 ring-1 ring-blue-500;
  }

  .custom-select-dropdown {
    @apply absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto;
  }

  .custom-select-option {
    @apply px-3 py-2 text-sm cursor-pointer hover:bg-gray-50 transition-colors duration-150;
  }

  .custom-select-option[data-selected="true"] {
    @apply bg-blue-50 text-blue-900;
  }

  .custom-select-option:hover {
    @apply bg-gray-50;
  }

  .custom-select-option[data-selected="true"]:hover {
    @apply bg-blue-100;
  }

  /* Dark mode support for custom select */
  .dark .custom-select-trigger {
    @apply bg-gray-800 border-gray-600 text-gray-100;
  }

  .dark .custom-select-trigger:hover {
    @apply border-gray-500;
  }

  .dark .custom-select-trigger[aria-expanded="true"] {
    @apply border-blue-400 ring-blue-400;
  }

  .dark .custom-select-dropdown {
    @apply bg-gray-800 border-gray-600;
  }

  .dark .custom-select-option {
    @apply text-gray-100 hover:bg-gray-700;
  }

  .dark .custom-select-option[data-selected="true"] {
    @apply bg-blue-900 text-blue-100;
  }

  .dark .custom-select-option[data-selected="true"]:hover {
    @apply bg-blue-800;
  }



  /* Hide any remaining native select elements */
  select:not(.keep-native) {
    @apply appearance-none;
  }

  /* Remove default select styling completely */
  select:not(.keep-native)::-ms-expand {
    display: none;
  }
}
