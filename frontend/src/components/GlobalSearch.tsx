import React, { useState, useRef, useEffect } from 'react'
import { Search, Clock, ArrowRight } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

interface SearchResult {
  id: string
  title: string
  description: string
  path: string
  type: 'page' | 'feature' | 'setting'
  category: string
}

interface RecentSearch {
  id: string
  query: string
  path: string
  timestamp: Date
}

const GlobalSearch: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([])
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const navigate = useNavigate()

  // All searchable items in the system
  const searchableItems: SearchResult[] = [
    // Dashboard
    { id: '1', title: 'Dashboard', description: 'Main overview and analytics', path: '/', type: 'page', category: 'Navigation' },

    // Integrations
    { id: '2', title: 'Integrations', description: 'Manage app connections', path: '/apps', type: 'page', category: 'Navigation' },
    { id: '3', title: 'All Apps', description: 'View all available integrations', path: '/apps', type: 'page', category: 'Integrations' },
    { id: '4', title: 'Connected Apps', description: 'View connected integrations', path: '/apps/connected', type: 'page', category: 'Integrations' },
    { id: '5', title: 'Available Apps', description: 'Browse available integrations', path: '/apps/available', type: 'page', category: 'Integrations' },

    // Ecommerce
    { id: '6', title: 'Orders', description: 'Manage customer orders', path: '/orders', type: 'page', category: 'Ecommerce' },
    { id: '7', title: 'All Products', description: 'Manage product catalog', path: '/products', type: 'page', category: 'Ecommerce' },
    { id: '7a', title: 'Price Lists', description: 'Consolidated price list management', path: '/products/price-lists', type: 'page', category: 'Ecommerce' },
    { id: '7a1', title: 'Markup', description: 'Set pricing markup rules', path: '/products/markup', type: 'page', category: 'Ecommerce' },
    { id: '7b', title: 'Inbound Products', description: 'Browse and import vendor products', path: '/products/shared/inbound', type: 'page', category: 'Ecommerce' },
    { id: '7c', title: 'Outbound Products', description: 'Share your products with vendors', path: '/products/shared/outbound', type: 'page', category: 'Ecommerce' },
    { id: '7d', title: 'Vendor Network', description: 'Manage vendor relationships', path: '/products/shared/vendors', type: 'page', category: 'Ecommerce' },
    { id: '8', title: 'Customers', description: 'Manage customer data', path: '/customers', type: 'page', category: 'Ecommerce' },
    { id: '8a', title: 'Storefront', description: 'Manage storefront settings', path: '/storefront', type: 'page', category: 'Ecommerce' },
    { id: '8b', title: 'Sales', description: 'View sales analytics', path: '/sales', type: 'page', category: 'Ecommerce' },

    // Integrations
    { id: '9', title: 'Integrations', description: 'Manage app connections', path: '/apps', type: 'page', category: 'Navigation' },

    // User Management
    { id: '10', title: 'User Management', description: 'Manage users and permissions', path: '/settings/users/my-organization/users', type: 'page', category: 'Navigation' },
    { id: '11', title: 'My Organization', description: 'Manage organization users', path: '/settings/users/my-organization/users', type: 'page', category: 'User Management' },
    { id: '12', title: 'Users', description: 'View and manage users', path: '/settings/users/my-organization/users', type: 'page', category: 'User Management' },
    { id: '13', title: 'Roles', description: 'Manage user roles', path: '/settings/users/my-organization/roles', type: 'page', category: 'User Management' },
    { id: '14', title: 'Permissions', description: 'Configure role permissions', path: '/settings/users/my-organization/roles', type: 'page', category: 'User Management' },
    { id: '15', title: 'Guest Organization', description: 'Manage guest users', path: '/settings/users/guest-organization/users', type: 'page', category: 'User Management' },

    // Settings
    { id: '16', title: 'Settings', description: 'System configuration', path: '/settings', type: 'page', category: 'Settings' },
    { id: '17', title: 'General Settings', description: 'General system settings', path: '/settings/general', type: 'page', category: 'Settings' },
    { id: '18', title: 'Jobs Management', description: 'Manage scheduled jobs', path: '/settings/jobs', type: 'page', category: 'Settings' },
    { id: '19', title: 'Security Settings', description: 'Security configuration', path: '/settings/security', type: 'page', category: 'Settings' },
    { id: '20', title: 'Notifications', description: 'Notification settings', path: '/settings/notifications', type: 'page', category: 'Settings' },
    { id: '21', title: 'Database', description: 'Database configuration', path: '/settings/database', type: 'page', category: 'Settings' },
  ]

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      try {
        const parsed = JSON.parse(saved).map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }))
        setRecentSearches(parsed.slice(0, 5)) // Keep only last 5
      } catch (error) {
        console.error('Failed to parse recent searches:', error)
      }
    }
  }, [])

  // Save recent searches to localStorage
  const saveRecentSearch = (query: string, path: string) => {
    const newSearch: RecentSearch = {
      id: Date.now().toString(),
      query,
      path,
      timestamp: new Date()
    }

    const updated = [newSearch, ...recentSearches.filter(s => s.query !== query)].slice(0, 5)
    setRecentSearches(updated)
    localStorage.setItem('recentSearches', JSON.stringify(updated))
  }

  // Search function
  useEffect(() => {
    if (query.trim() === '') {
      setResults([])
      return
    }

    const filtered = searchableItems.filter(item =>
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase()) ||
      item.category.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 8) // Limit to 8 results

    setResults(filtered)
  }, [query])

  // Handle navigation
  const handleNavigate = (path: string, title: string) => {
    navigate(path)
    saveRecentSearch(title, path)
    setIsOpen(false)
    setQuery('')
  }

  // Handle recent search click
  const handleRecentSearch = (recent: RecentSearch) => {
    navigate(recent.path)
    setIsOpen(false)
    setQuery('')
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault()
        setIsOpen(true)
        inputRef.current?.focus()
      }
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  return (
    <div className="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start" ref={searchRef}>
      <div
        className={`transition-all duration-300 ease-in-out ${
          isHovered ? 'w-full lg:w-80' : 'w-full lg:w-60'
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            ref={inputRef}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm transition-all duration-300"
            placeholder="Search..."
            type="search"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setIsOpen(true)}
          />

          {isOpen && (
            <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-y-auto">
              {query.trim() === '' ? (
                // Show recent searches when no query
                <div className="p-4">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Recent Searches</h3>
                  {recentSearches.length > 0 ? (
                    <div className="space-y-2">
                      {recentSearches.map((recent) => (
                        <button
                          key={recent.id}
                          onClick={() => handleRecentSearch(recent)}
                          className="w-full flex items-center justify-between p-2 text-left hover:bg-gray-50 rounded-md transition-colors"
                        >
                          <div className="flex items-center space-x-3">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-900">{recent.query}</span>
                          </div>
                          <ArrowRight className="h-4 w-4 text-gray-400" />
                        </button>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No recent searches</p>
                  )}
                </div>
              ) : (
                // Show search results
                <div className="p-4">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">
                    Search Results ({results.length})
                  </h3>
                  {results.length > 0 ? (
                    <div className="space-y-2">
                      {results.map((result) => (
                        <button
                          key={result.id}
                          onClick={() => handleNavigate(result.path, result.title)}
                          className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 rounded-md transition-colors"
                        >
                          <div>
                            <div className="text-sm font-medium text-gray-900">{result.title}</div>
                            <div className="text-xs text-gray-500">{result.description}</div>
                            <div className="text-xs text-blue-600 mt-1">{result.category}</div>
                          </div>
                          <ArrowRight className="h-4 w-4 text-gray-400" />
                        </button>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No results found for "{query}"</p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default GlobalSearch
