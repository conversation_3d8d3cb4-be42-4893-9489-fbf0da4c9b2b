import { useState, useEffect } from 'react'
import { X, Search, Plus, Check, Setting<PERSON>, Eye, EyeOff } from 'lucide-react'
import toast from 'react-hot-toast'

interface CustomField {
  id: string
  label: string
  key: string
  field_type: string
  resource_type: string
  namespace: string
  description?: string
  is_required: boolean
  is_searchable: boolean
  is_filterable: boolean
  is_ai_enabled: boolean
  is_active: boolean
  field_options?: any
}

interface FieldManagementModalProps {
  isOpen: boolean
  onClose: () => void
  resourceType: string
  activeFields: CustomField[]
  onFieldsChange: (fields: CustomField[]) => void
  onCreateNewField: () => void
}

const FieldManagementModal: React.FC<FieldManagementModalProps> = ({
  isOpen,
  onClose,
  resourceType,
  activeFields,
  onFieldsChange,
  onCreateNewField
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [availableFields, setAvailableFields] = useState<CustomField[]>([])
  const [loading, setLoading] = useState(true)

  // Sample available fields - replace with API call
  const sampleAvailableFields: CustomField[] = [
    {
      id: '1',
      label: 'SEO Title',
      key: 'seo_title',
      field_type: 'text',
      resource_type: 'products',
      namespace: 'global',
      description: 'Custom SEO title for search engines',
      is_required: false,
      is_searchable: true,
      is_filterable: false,
      is_ai_enabled: true,
      is_active: true
    },
    {
      id: '2',
      label: 'Warranty Period',
      key: 'warranty_period',
      field_type: 'number',
      resource_type: 'products',
      namespace: 'global',
      description: 'Warranty period in months',
      is_required: false,
      is_searchable: false,
      is_filterable: true,
      is_ai_enabled: false,
      is_active: true
    },
    {
      id: '7',
      label: 'Brand',
      key: 'brand',
      field_type: 'single_select',
      resource_type: 'products',
      namespace: 'global',
      description: 'Product brand',
      is_required: false,
      is_searchable: true,
      is_filterable: true,
      is_ai_enabled: false,
      is_active: true,
      field_options: {
        options: [
          { value: 'apple', label: 'Apple' },
          { value: 'samsung', label: 'Samsung' },
          { value: 'sony', label: 'Sony' }
        ]
      }
    },
    {
      id: '8',
      label: 'Material',
      key: 'material',
      field_type: 'text',
      resource_type: 'products',
      namespace: 'global',
      description: 'Product material',
      is_required: false,
      is_searchable: true,
      is_filterable: true,
      is_ai_enabled: false,
      is_active: true
    },
    {
      id: '9',
      label: 'Dimensions',
      key: 'dimensions',
      field_type: 'text',
      resource_type: 'products',
      namespace: 'global',
      description: 'Product dimensions (L x W x H)',
      is_required: false,
      is_searchable: false,
      is_filterable: false,
      is_ai_enabled: false,
      is_active: true
    },
    {
      id: '10',
      label: 'Weight',
      key: 'weight',
      field_type: 'number',
      resource_type: 'products',
      namespace: 'global',
      description: 'Product weight in kg',
      is_required: false,
      is_searchable: false,
      is_filterable: true,
      is_ai_enabled: false,
      is_active: true
    }
  ]

  useEffect(() => {
    if (isOpen) {
      fetchAvailableFields()
    }
  }, [isOpen, resourceType])

  const fetchAvailableFields = async () => {
    try {
      setLoading(true)
      
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/custom-fields?resource_type=${resourceType}&include_global=true`)
      // const data = await response.json()
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const relevantFields = sampleAvailableFields.filter(field => 
        field.resource_type === resourceType || field.resource_type === 'global'
      )
      
      setAvailableFields(relevantFields)
    } catch (error) {
      console.error('Error fetching available fields:', error)
      toast.error('Failed to load available fields')
    } finally {
      setLoading(false)
    }
  }

  const getFieldTypeDisplay = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'text': 'Text',
      'number': 'Number',
      'single_select': 'Single Select',
      'multi_select': 'Multi Select',
      'date': 'Date',
      'boolean': 'Boolean',
      'textarea': 'Textarea',
      'url': 'URL',
      'email': 'Email'
    }
    return typeMap[type] || type
  }

  const getFieldTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
      'text': 'bg-blue-100 text-blue-800',
      'number': 'bg-green-100 text-green-800',
      'single_select': 'bg-purple-100 text-purple-800',
      'multi_select': 'bg-purple-100 text-purple-800',
      'date': 'bg-orange-100 text-orange-800',
      'boolean': 'bg-gray-100 text-gray-800',
      'textarea': 'bg-blue-100 text-blue-800',
      'url': 'bg-indigo-100 text-indigo-800',
      'email': 'bg-pink-100 text-pink-800'
    }
    return colorMap[type] || 'bg-gray-100 text-gray-800'
  }

  const isFieldActive = (field: CustomField) => {
    return activeFields.some(activeField => activeField.id === field.id)
  }

  const toggleField = (field: CustomField) => {
    const isActive = isFieldActive(field)
    
    if (isActive) {
      // Remove field
      const newActiveFields = activeFields.filter(activeField => activeField.id !== field.id)
      onFieldsChange(newActiveFields)
      toast.success(`${field.label} removed from form`)
    } else {
      // Add field
      const newActiveFields = [...activeFields, field]
      onFieldsChange(newActiveFields)
      toast.success(`${field.label} added to form`)
    }
  }

  const filteredFields = availableFields.filter(field =>
    field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    field.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
    field.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Manage Form Fields</h2>
            <p className="text-sm text-gray-500 mt-1">
              Add or remove custom fields for {resourceType} forms
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Search and Actions */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search available fields..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button
              onClick={onCreateNewField}
              className="btn btn-primary flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create New Field
            </button>
          </div>
        </div>

        {/* Fields List */}
        <div className="flex-1 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 200px)' }}>
          {loading ? (
            <div className="p-6">
              <div className="animate-pulse space-y-4">
                {[1, 2, 3, 4, 5].map(i => (
                  <div key={i} className="flex items-center space-x-4">
                    <div className="h-4 w-4 bg-gray-200 rounded"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                    <div className="h-6 w-16 bg-gray-200 rounded-full"></div>
                  </div>
                ))}
              </div>
            </div>
          ) : filteredFields.length === 0 ? (
            <div className="p-12 text-center">
              <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No fields found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm ? 'Try adjusting your search terms' : 'No custom fields available for this resource type'}
              </p>
              <button
                onClick={onCreateNewField}
                className="btn btn-primary flex items-center mx-auto"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Field
              </button>
            </div>
          ) : (
            <div className="p-6 space-y-4">
              {filteredFields.map((field) => {
                const isActive = isFieldActive(field)
                
                return (
                  <div
                    key={field.id}
                    className={`flex items-center space-x-4 p-4 border rounded-lg transition-colors ${
                      isActive 
                        ? 'border-blue-200 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <button
                      onClick={() => toggleField(field)}
                      className={`flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                        isActive
                          ? 'border-blue-500 bg-blue-500 text-white'
                          : 'border-gray-300 hover:border-blue-500'
                      }`}
                    >
                      {isActive && <Check className="h-3 w-3" />}
                    </button>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-1">
                        <h4 className="text-sm font-medium text-gray-900">{field.label}</h4>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getFieldTypeColor(field.field_type)}`}>
                          {getFieldTypeDisplay(field.field_type)}
                        </span>
                        {field.is_required && (
                          <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                            Required
                          </span>
                        )}
                      </div>
                      <p className="text-xs text-gray-500">{field.key}</p>
                      {field.description && (
                        <p className="text-sm text-gray-600 mt-1">{field.description}</p>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      {isActive ? (
                        <Eye className="h-4 w-4 text-blue-600" title="Active in form" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-gray-400" title="Not in form" />
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-500">
            {activeFields.length} field{activeFields.length !== 1 ? 's' : ''} active in this form
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="btn btn-secondary"
            >
              Done
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FieldManagementModal
