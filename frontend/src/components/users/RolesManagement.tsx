import { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Shield, Users } from 'lucide-react'
import { mockRoles } from '../../data/mockAuthData'
import { Role, Permission } from '../../types/auth'
import ColorPicker from '../ColorPicker'
import toast from 'react-hot-toast'

interface CreateRoleModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (role: Partial<Role>) => void
  editingRole?: Role | null
}

const CreateRoleModal = ({ isOpen, onClose, onSave, editingRole }: CreateRoleModalProps) => {
  const [formData, setFormData] = useState({
    name: editingRole?.name || '',
    description: editingRole?.description || '',
    color: '#3B82F6',
    icon: '👤'
  })

  if (!isOpen) return null

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {editingRole ? 'Edit Role' : 'Create New Role'}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Role Name *
            </label>
            <input
              type="text"
              required
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., Content Manager"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              required
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe the role's responsibilities..."
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <ColorPicker
                label="Color"
                value={formData.color}
                onChange={(color) => setFormData({ ...formData, color })}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Icon
              </label>
              <div className="space-y-3">
                {/* Icon Preview */}
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center text-lg">
                    {formData.icon}
                  </div>
                  <span className="text-sm text-gray-600">Preview</span>
                </div>

                {/* Icon Options */}
                <div className="grid grid-cols-6 gap-2">
                  {['👤', '👑', '🔧', '💻', '📊', '👁️', '🛡️', '⚙️', '📝', '🎯', '🚀', '💼'].map((icon) => (
                    <button
                      key={icon}
                      type="button"
                      onClick={() => setFormData({ ...formData, icon })}
                      className={`w-8 h-8 rounded-lg border-2 transition-all duration-200 hover:scale-110 flex items-center justify-center text-lg ${
                        formData.icon === icon
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                      title={icon}
                    >
                      {icon}
                    </button>
                  ))}
                </div>

                {/* Custom Icon Input */}
                <input
                  type="text"
                  value={formData.icon}
                  onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                  className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Or type custom emoji..."
                />
              </div>
            </div>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200"
            >
              {editingRole ? 'Update Role' : 'Create Role'}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

const RolesManagement = () => {
  const [roles, setRoles] = useState<Role[]>(mockRoles)
  const [searchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState<Role | null>(mockRoles[0])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [activeTab, setActiveTab] = useState<'permissions' | 'users'>('permissions')
  const [pendingPermissions, setPendingPermissions] = useState<Permission[]>([])
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Permission groups based on navigation modules
  const permissionGroups = [
    {
      name: 'Orders',
      permissions: [
        { key: 'view_orders', name: 'View Orders', actions: ['view'] },
        { key: 'order_price_report', name: 'Order Price Report', actions: ['view'] },
        { key: 'bulk_orders', name: 'Bulk Orders', actions: ['view', 'create'] },
        { key: 'manage_products', name: 'Manage Products', actions: ['view', 'create', 'write', 'update', 'delete', 'manage'] },
        { key: 'po_request', name: 'PO Request', actions: ['view', 'create'] },
        { key: 'pending', name: 'Pending', actions: ['view'] },
        { key: 'completed', name: 'Completed', actions: ['view'] },
        { key: 'deleted', name: 'Deleted', actions: ['view'] }
      ]
    },
    {
      name: 'Products',
      permissions: [
        { key: 'product_catalog', name: 'Product Catalog', actions: ['view'] },
        { key: 'inventory_management', name: 'Inventory Management', actions: ['view', 'create', 'write', 'update', 'delete', 'manage'] }
      ]
    },
    {
      name: 'Integrations',
      permissions: [
        { key: 'view_integrations', name: 'View Integrations', actions: ['view'] },
        { key: 'manage_integrations', name: 'Manage Integrations', actions: ['view', 'create', 'write', 'update', 'delete', 'manage'] }
      ]
    },
    {
      name: 'Data Sync',
      permissions: [
        { key: 'view_jobs', name: 'View Jobs', actions: ['view'] },
        { key: 'manage_jobs', name: 'Manage Jobs', actions: ['view', 'create', 'write', 'update', 'delete', 'manage'] }
      ]
    },
    {
      name: 'Analytics',
      permissions: [
        { key: 'view_analytics', name: 'View Analytics', actions: ['view'] },
        { key: 'export_analytics', name: 'Export Analytics', actions: ['view', 'export'] }
      ]
    },
    {
      name: 'Reports',
      permissions: [
        { key: 'view_reports', name: 'View Reports', actions: ['view'] },
        { key: 'create_reports', name: 'Create Reports', actions: ['view', 'create'] },
        { key: 'export_reports', name: 'Export Reports', actions: ['view', 'export'] }
      ]
    },
    {
      name: 'User Management',
      permissions: [
        { key: 'view_users', name: 'View Users', actions: ['view'] },
        { key: 'create_users', name: 'Create Users', actions: ['view', 'create'] },
        { key: 'edit_users', name: 'Edit Users', actions: ['view', 'write', 'update'] },
        { key: 'delete_users', name: 'Delete Users', actions: ['view', 'delete'] },
        { key: 'invite_users', name: 'Invite Users', actions: ['view', 'create'] }
      ]
    },
    {
      name: 'Settings',
      permissions: [
        { key: 'view_settings', name: 'View Settings', actions: ['view'] },
        { key: 'edit_settings', name: 'Edit Settings', actions: ['view', 'write', 'update'] },
        { key: 'system_config', name: 'System Configuration', actions: ['view', 'manage'] }
      ]
    }
  ]



  const hasPermission = (roleId: string, permissionKey: string, action: string): boolean => {
    const role = roles.find(r => r.id === roleId)
    if (!role) return false

    // Check pending permissions first, then fall back to saved permissions
    const currentPermissions = selectedRole?.id === roleId ? pendingPermissions : role.permissions
    return currentPermissions.some(p => p.resource === permissionKey && p.action === action)
  }

  const togglePermission = (roleId: string, permissionKey: string, action: string) => {
    const role = roles.find(r => r.id === roleId)
    if (!role || role.isSystem) return

    const permissionExists = hasPermission(roleId, permissionKey, action)
    let updatedPermissions: Permission[]

    if (permissionExists) {
      updatedPermissions = pendingPermissions.filter(
        p => !(p.resource === permissionKey && p.action === action)
      )
    } else {
      const newPermission: Permission = {
        id: `${Date.now()}-${Math.random()}`,
        resource: permissionKey,
        action,
        scope: 'workspace'
      }
      updatedPermissions = [...pendingPermissions, newPermission]
    }

    setPendingPermissions(updatedPermissions)
    setHasUnsavedChanges(true)
  }

  const handleCreateRole = (roleData: Partial<Role>) => {
    const newRole: Role = {
      id: `role_${Date.now()}`,
      name: roleData.name!,
      description: roleData.description!,
      permissions: [],
      isSystem: false,
      level: 'workspace',
      isCustom: true,
      metadata: {
        category: 'custom'
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    setRoles([...roles, newRole])
    setSelectedRole(newRole)
    toast.success('Role created successfully!')
  }

  const handleEditRole = (roleData: Partial<Role>) => {
    if (!editingRole) return

    const updatedRoles = roles.map(role =>
      role.id === editingRole.id
        ? { ...role, ...roleData, updatedAt: new Date().toISOString() }
        : role
    )
    setRoles(updatedRoles)
    if (selectedRole?.id === editingRole.id) {
      setSelectedRole({ ...editingRole, ...roleData })
    }
    setEditingRole(null)
    toast.success('Role updated successfully!')
  }

  const handleDeleteRole = (roleId: string) => {
    const role = roles.find(r => r.id === roleId)
    if (role?.isSystem) {
      toast.error('Cannot delete system roles')
      return
    }

    if (confirm('Are you sure you want to delete this role?')) {
      setRoles(roles.filter(role => role.id !== roleId))
      if (selectedRole?.id === roleId) {
        setSelectedRole(roles[0])
      }
      toast.success('Role deleted successfully!')
    }
  }

  const handleSavePermissions = () => {
    if (!selectedRole) return

    const updatedRole = {
      ...selectedRole,
      permissions: pendingPermissions,
      updatedAt: new Date().toISOString()
    }
    const updatedRoles = roles.map(r => r.id === selectedRole.id ? updatedRole : r)

    setRoles(updatedRoles)
    setSelectedRole(updatedRole)
    setHasUnsavedChanges(false)
    toast.success('Permissions saved successfully!')
  }



  const handleRoleSelect = (role: Role) => {
    if (hasUnsavedChanges) {
      if (confirm('You have unsaved changes. Do you want to discard them?')) {
        setHasUnsavedChanges(false)
        setSelectedRole(role)
        setPendingPermissions(role.permissions)
      }
    } else {
      setSelectedRole(role)
      setPendingPermissions(role.permissions)
    }
  }

  // Initialize pending permissions when selected role changes
  useEffect(() => {
    if (selectedRole && !hasUnsavedChanges) {
      setPendingPermissions(selectedRole.permissions)
    }
  }, [selectedRole, hasUnsavedChanges])

  return (
    <div>
      {/* Header with Save and Create Role Buttons */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Select Role</h2>
        <div className="flex items-center space-x-3">
          {hasUnsavedChanges && !selectedRole?.isSystem && (
            <button
              onClick={handleSavePermissions}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm font-medium"
            >
              Save Changes
            </button>
          )}
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Role
          </button>
        </div>
      </div>

      {/* Main Layout: Compact Sidebar + Details */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Side - Role Selection Sidebar */}
        <div className="lg:col-span-1">
          <div className="space-y-3">
            {filteredRoles.map((role) => {
              const getRoleIcon = (roleName: string) => {
                switch (roleName.toLowerCase()) {
                  case 'workspace owner':
                    return '👑'
                  case 'integration manager':
                    return '🔧'
                  case 'developer':
                    return '💻'
                  case 'data analyst':
                    return '📊'
                  case 'viewer':
                    return '👁️'
                  default:
                    return '👤'
                }
              }



              const isSelected = selectedRole?.id === role.id

              return (
                <button
                  key={role.id}
                  onClick={() => handleRoleSelect(role)}
                  className={`w-full text-left px-4 py-4 rounded-lg border transition-colors duration-200 ${
                    isSelected
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="mr-2 text-sm">{getRoleIcon(role.name)}</span>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{role.name}</div>
                      </div>
                    </div>
                    <span className="text-xs text-gray-500">
                      {0}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1 truncate">
                    {role.description}
                  </p>
                </button>
              )
            })}
          </div>
        </div>

        {/* Right Side - Role Details */}
        <div className="lg:col-span-3">
          {selectedRole ? (
            <div className="bg-white border border-gray-200 rounded-lg">
              {/* Role Header */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-start justify-between">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">{selectedRole.name}</h2>
                    <p className="text-sm text-gray-600 mt-1">{selectedRole.description}</p>
                    {selectedRole.isSystem && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mt-2">
                        System Role
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setEditingRole(selectedRole)}
                      disabled={selectedRole.isSystem}
                      className="text-gray-400 hover:text-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    {!selectedRole.isSystem && (
                      <button
                        onClick={() => handleDeleteRole(selectedRole.id)}
                        className="text-gray-400 hover:text-red-600"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Permission Tabs */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  <button
                    onClick={() => setActiveTab('permissions')}
                    className={`border-b-2 py-3 px-1 text-sm font-medium transition-colors duration-200 ${
                      activeTab === 'permissions'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Permission Set
                  </button>
                  <button
                    onClick={() => setActiveTab('users')}
                    className={`border-b-2 py-3 px-1 text-sm font-medium transition-colors duration-200 ${
                      activeTab === 'users'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Users (0)
                  </button>
                </nav>
              </div>

              {/* Tab Content */}
              {activeTab === 'permissions' ? (
                /* Permission Matrix */
                <div className="p-6">
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-900 mb-4">Module / Feature</h3>

                    {/* Permission Headers */}
                    <div className="grid grid-cols-[2fr_1fr_1fr_1fr_1fr_1fr_1fr_1fr] gap-4 mb-4 pb-2 border-b border-gray-200">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">MODULE</div>
                      <div className="text-center text-xs font-medium text-gray-500 uppercase tracking-wider">View</div>
                      <div className="text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Create</div>
                      <div className="text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Write</div>
                      <div className="text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Update</div>
                      <div className="text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Delete</div>
                      <div className="text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Manage</div>
                      <div className="text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Export</div>
                    </div>
                  </div>

                  {/* Permission Groups */}
                  {permissionGroups.map((group) => (
                    <div key={group.name} className="mb-8">
                      {/* Group Header */}
                      <div className="flex items-center mb-4">
                        <div className="w-4 h-4 bg-orange-200 rounded mr-3"></div>
                        <h4 className="text-sm font-medium text-gray-900">{group.name}</h4>
                      </div>

                      {/* Group Permissions */}
                      {group.permissions.map((permission) => (
                        <div key={permission.key} className="grid grid-cols-[2fr_1fr_1fr_1fr_1fr_1fr_1fr_1fr] gap-4 items-center py-3 border-b border-gray-100 last:border-b-0">
                          <div className="text-sm text-gray-700 pl-6">{permission.name}</div>

                          {/* Permission Checkboxes */}
                          {['view', 'create', 'write', 'update', 'delete', 'manage', 'export'].map((action) => (
                            <div key={action} className="flex justify-center">
                              {permission.actions.includes(action) ? (
                                <button
                                  onClick={() => togglePermission(selectedRole.id, permission.key, action)}
                                  disabled={selectedRole.isSystem}
                                  className={`h-5 w-5 rounded border-2 flex items-center justify-center transition-colors duration-200 ${
                                    hasPermission(selectedRole.id, permission.key, action)
                                      ? 'bg-blue-600 border-blue-600 text-white'
                                      : 'border-gray-300 hover:border-gray-400'
                                  } ${selectedRole.isSystem ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                                >
                                  {hasPermission(selectedRole.id, permission.key, action) && (
                                    <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                  )}
                                </button>
                              ) : (
                                <div className="h-5 w-5"></div>
                              )}
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  ))}

                  {/* Unsaved Changes Warning */}
                  {hasUnsavedChanges && !selectedRole.isSystem && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-md border border-amber-200 inline-flex items-center">
                        ⚠️ You have unsaved changes. Use the "Save Changes" button above to save your modifications.
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                /* Users Tab */
                <div className="p-6">
                  <div className="text-center py-8">
                    <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Users with this role</h3>
                    <p className="text-gray-600 mb-4">
                      0 users have been assigned the "{selectedRole.name}" role
                    </p>
                    <div className="text-sm text-gray-500">
                      User management functionality coming soon...
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white border border-gray-200 rounded-lg p-12 text-center">
              <Shield className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Role Selected</h3>
              <p className="text-gray-600 mb-4">
                Select a role from the list to view its details and permissions,<br />
                or create a new role to customize access.
              </p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Role
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Create/Edit Role Modal */}
      <CreateRoleModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSave={handleCreateRole}
      />

      <CreateRoleModal
        isOpen={!!editingRole}
        onClose={() => setEditingRole(null)}
        onSave={handleEditRole}
        editingRole={editingRole}
      />
    </div>
  )
}

export default RolesManagement
