import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Search, MoreVertical, ExternalLink, Clock, CheckCircle, UserPlus, Mail } from 'lucide-react'
import CustomSelect from '../CustomSelect'
import InviteGuestModal from '../InviteGuestModal'

import PermissionGate from '../PermissionGate'
import { RESOURCES, ACTIONS } from '../../types/auth'
import toast from 'react-hot-toast'

const GuestOrganizationUsers = () => {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedOrganization, setSelectedOrganization] = useState('')
  const [selectedAccessLevel, setSelectedAccessLevel] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [showInviteModal, setShowInviteModal] = useState(false)

  const handleUserClick = (userId: string) => {
    navigate(`/settings/users/guest-organization/users/${userId}`)
  }

  const handleInviteGuest = () => {
    setShowInviteModal(true)
  }

  // Dropdown options
  const organizationOptions = [
    { value: '', label: 'All Organizations' },
    { value: 'techstart', label: 'TechStart Inc' },
    { value: 'globalcorp', label: 'Global Corp' },
    { value: 'innovation', label: 'Innovation Labs' }
  ]

  const accessLevelOptions = [
    { value: '', label: 'All Access Levels' },
    { value: 'read-only', label: 'Read Only' },
    { value: 'limited', label: 'Limited Access' },
    { value: 'full', label: 'Full Access' }
  ]

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'pending', label: 'Pending' },
    { value: 'expired', label: 'Expired' }
  ]

  return (
    <div>
      <div className="mb-6">
        <p className="text-sm text-gray-600">
          View and manage guest users from other organizations
        </p>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search guest users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 w-64"
            />
          </div>

          {/* Filters */}
          <div className="w-48">
            <CustomSelect
              options={organizationOptions}
              value={selectedOrganization}
              onChange={setSelectedOrganization}
              size="sm"
            />
          </div>

          <div className="w-48">
            <CustomSelect
              options={accessLevelOptions}
              value={selectedAccessLevel}
              onChange={setSelectedAccessLevel}
              size="sm"
            />
          </div>

          <div className="w-48">
            <CustomSelect
              options={statusOptions}
              value={selectedStatus}
              onChange={setSelectedStatus}
              size="sm"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-3">
          <PermissionGate resource={RESOURCES.USERS} action={ACTIONS.INVITE}>
            <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <Mail className="h-4 w-4 mr-2" />
              Email Selected
            </button>
            <button
              onClick={handleInviteGuest}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Invite Guest
            </button>
          </PermissionGate>
        </div>
      </div>

      {/* Guest Users Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Email
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Organization
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Access Level
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Expires
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">

              </th>
            </tr>
          </thead>
          <tbody className="bg-white">
            {/* Sample guest users */}
            <tr
              className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
              onClick={() => handleUserClick('guest-1')}
            >
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">MW</span>
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">Mike Wilson</div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <EMAIL>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <span className="text-sm text-gray-900">TechStart Inc</span>
                  <ExternalLink className="h-3 w-3 text-gray-400 ml-1" />
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Read Only
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Active
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="flex items-center">
                  <Clock className="h-3 w-3 text-gray-400 mr-1" />
                  Jun 15, 2025
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button className="text-gray-400 hover:text-gray-600">
                  <MoreVertical className="h-4 w-4" />
                </button>
              </td>
            </tr>

            <tr
              className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
              onClick={() => handleUserClick('guest-2')}
            >
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-pink-500 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">AL</span>
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">Anna Lee</div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <EMAIL>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <span className="text-sm text-gray-900">Global Corp</span>
                  <ExternalLink className="h-3 w-3 text-gray-400 ml-1" />
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Limited Access
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  <Clock className="h-3 w-3 mr-1" />
                  Pending
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="flex items-center">
                  <Clock className="h-3 w-3 text-gray-400 mr-1" />
                  Jul 01, 2025
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button className="text-gray-400 hover:text-gray-600">
                  <MoreVertical className="h-4 w-4" />
                </button>
              </td>
            </tr>

            <tr
              className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
              onClick={() => handleUserClick('guest-3')}
            >
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-teal-500 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">RK</span>
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">Robert Kim</div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <EMAIL>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <span className="text-sm text-gray-900">Innovation Labs</span>
                  <ExternalLink className="h-3 w-3 text-gray-400 ml-1" />
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Full Access
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Expired
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="flex items-center">
                  <Clock className="h-3 w-3 text-gray-400 mr-1" />
                  May 01, 2025
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button className="text-gray-400 hover:text-gray-600">
                  <MoreVertical className="h-4 w-4" />
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Empty state if no guest users */}
      {/* <div className="text-center py-12">
        <ExternalLink className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No guest users</h3>
        <p className="mt-1 text-sm text-gray-500">
          Guest users from other organizations will appear here when they're granted access.
        </p>
      </div> */}

      {/* Invite Guest Modal */}
      {showInviteModal && (
        <InviteGuestModal
          onClose={() => setShowInviteModal(false)}
          onInvite={() => {
            toast.success('Guest invitation sent successfully!')
            setShowInviteModal(false)
          }}
        />
      )}
    </div>
  )
}

export default GuestOrganizationUsers
