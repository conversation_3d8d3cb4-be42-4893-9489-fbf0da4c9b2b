import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Shield, ArrowRight } from 'lucide-react'

const PermissionsManagement = () => {
  const navigate = useNavigate()

  // Auto-redirect to roles page after a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      navigate('/settings/users/my-organization/roles', { replace: true })
    }, 3000)

    return () => clearTimeout(timer)
  }, [navigate])

  const handleRedirectNow = () => {
    navigate('/settings/users/my-organization/roles', { replace: true })
  }

  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center max-w-md">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-6">
          <Shield className="h-8 w-8 text-blue-600" />
        </div>

        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Permissions Management Moved
        </h3>

        <p className="text-sm text-gray-600 mb-6">
          Permission management has been consolidated into the Roles section for a better, unified experience.
          You can now manage both roles and their permissions in one place.
        </p>

        <div className="space-y-3">
          <button
            onClick={handleRedirectNow}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm font-medium"
          >
            Go to Roles & Permissions
            <ArrowRight className="h-4 w-4 ml-2" />
          </button>

          <p className="text-xs text-gray-500">
            Redirecting automatically in 3 seconds...
          </p>
        </div>
      </div>
    </div>
  )
}

export default PermissionsManagement
