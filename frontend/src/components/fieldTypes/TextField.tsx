import React from 'react'
import { FieldProps } from './index'

const TextField: React.FC<FieldProps> = ({
  id,
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  className = '',
  error,
  validation
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    
    // Apply validation if provided
    if (validation) {
      if (validation.maxLength && newValue.length > validation.maxLength) {
        return // Don't update if exceeds max length
      }
      if (validation.pattern) {
        const regex = new RegExp(validation.pattern)
        if (newValue && !regex.test(newValue)) {
          return // Don't update if doesn't match pattern
        }
      }
    }
    
    onChange(newValue)
  }

  const baseClasses = "w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
  const errorClasses = error ? "border-red-300 focus:ring-red-500 focus:border-red-500" : "border-gray-300"
  const disabledClasses = disabled ? "bg-gray-100 cursor-not-allowed" : ""

  return (
    <div className="space-y-1">
      <input
        id={id}
        type="text"
        value={value || ''}
        onChange={handleChange}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        className={`${baseClasses} ${errorClasses} ${disabledClasses} ${className}`}
      />
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      {validation?.maxLength && (
        <p className="text-xs text-gray-500">
          {(value || '').length}/{validation.maxLength} characters
        </p>
      )}
    </div>
  )
}

export default TextField
