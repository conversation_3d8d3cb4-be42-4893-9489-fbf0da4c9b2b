import { Role } from '../types/auth'

interface RoleBadgeProps {
  role: Role
  size?: 'sm' | 'md' | 'lg'
  showIcon?: boolean
  className?: string
}

const RoleBadge = ({ role, size = 'md', showIcon = true, className = '' }: RoleBadgeProps) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-0.5 text-xs'
      case 'lg':
        return 'px-3 py-1.5 text-sm'
      default:
        return 'px-2.5 py-1 text-xs'
    }
  }

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'text-xs'
      case 'lg':
        return 'text-sm'
      default:
        return 'text-xs'
    }
  }

  const getRoleStyles = (roleId: string) => {
    const styles: Record<string, { bg: string; text: string; border: string }> = {
      'role_workspace_owner': {
        bg: 'bg-red-50',
        text: 'text-red-700',
        border: 'border-red-200'
      },
      'role_integration_manager': {
        bg: 'bg-blue-50',
        text: 'text-blue-700',
        border: 'border-blue-200'
      },
      'role_developer': {
        bg: 'bg-green-50',
        text: 'text-green-700',
        border: 'border-green-200'
      },
      'role_analyst': {
        bg: 'bg-purple-50',
        text: 'text-purple-700',
        border: 'border-purple-200'
      },
      'role_viewer': {
        bg: 'bg-gray-50',
        text: 'text-gray-700',
        border: 'border-gray-200'
      },
      'super_admin': {
        bg: 'bg-red-100',
        text: 'text-red-800',
        border: 'border-red-300'
      },
      'org_owner': {
        bg: 'bg-orange-50',
        text: 'text-orange-700',
        border: 'border-orange-200'
      },
      'org_admin': {
        bg: 'bg-orange-50',
        text: 'text-orange-600',
        border: 'border-orange-200'
      },
      'billing_manager': {
        bg: 'bg-yellow-50',
        text: 'text-yellow-700',
        border: 'border-yellow-200'
      }
    }

    return styles[roleId] || {
      bg: 'bg-gray-50',
      text: 'text-gray-700',
      border: 'border-gray-200'
    }
  }

  const styles = getRoleStyles(role.id)

  return (
    <span
      className={`
        inline-flex items-center font-medium rounded-full border
        ${getSizeClasses()}
        ${styles.bg}
        ${styles.text}
        ${styles.border}
        ${className}
      `}
    >
      {showIcon && role.metadata?.icon && (
        <span className={`mr-1 ${getIconSize()}`}>
          {role.metadata.icon}
        </span>
      )}
      {role.name}
    </span>
  )
}

export default RoleBadge
