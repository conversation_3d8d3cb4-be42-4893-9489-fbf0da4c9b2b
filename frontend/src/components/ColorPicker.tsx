import { useState, useRef, useEffect } from 'react'
import { ChevronDown, Palette, Co<PERSON>, Check } from 'lucide-react'

interface ColorPickerProps {
  value: string
  onChange: (color: string) => void
  label?: string
}

interface HSL {
  h: number
  s: number
  l: number
}

interface RGB {
  r: number
  g: number
  b: number
}

const ColorPicker = ({ value, onChange, label }: ColorPickerProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<'palette' | 'custom'>('palette')
  const [copied, setCopied] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Predefined color palette
  const colorPalette = [
    '#3B82F6', '#1D4ED8', '#1E40AF', '#1E3A8A', // Blues
    '#10B981', '#059669', '#047857', '#065F46', // Greens
    '#F59E0B', '#D97706', '#B45309', '#92400E', // Yellows/Oranges
    '#EF4444', '#DC2626', '#B91C1C', '#991B1B', // Reds
    '#8B5CF6', '#7C3AED', '#6D28D9', '#5B21B6', // Purples
    '#06B6D4', '#0891B2', '#0E7490', '#155E75', // Cyans
    '#EC4899', '#DB2777', '#BE185D', '#9D174D', // Pinks
    '#6B7280', '#4B5563', '#374151', '#1F2937', // Grays
  ]

  // Convert hex to RGB
  const hexToRgb = (hex: string): RGB => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 }
  }

  // Convert RGB to hex
  const rgbToHex = (r: number, g: number, b: number): string => {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
  }

  // Convert RGB to HSL
  const rgbToHsl = (r: number, g: number, b: number): HSL => {
    r /= 255
    g /= 255
    b /= 255
    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    let h = 0, s = 0, l = (max + min) / 2

    if (max !== min) {
      const d = max - min
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break
        case g: h = (b - r) / d + 2; break
        case b: h = (r - g) / d + 4; break
      }
      h /= 6
    }

    return { h: h * 360, s: s * 100, l: l * 100 }
  }

  // Convert HSL to RGB
  const hslToRgb = (h: number, s: number, l: number): RGB => {
    h /= 360
    s /= 100
    l /= 100
    const a = s * Math.min(l, 1 - l)
    const f = (n: number) => {
      const k = (n + h * 12) % 12
      return l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1)
    }
    return {
      r: Math.round(f(0) * 255),
      g: Math.round(f(8) * 255),
      b: Math.round(f(4) * 255)
    }
  }

  const currentRgb = hexToRgb(value)
  const currentHsl = rgbToHsl(currentRgb.r, currentRgb.g, currentRgb.b)

  const updateColor = (rgb: RGB) => {
    const hex = rgbToHex(rgb.r, rgb.g, rgb.b)
    onChange(hex)
  }

  const updateFromHsl = (hsl: HSL) => {
    const rgb = hslToRgb(hsl.h, hsl.s, hsl.l)
    updateColor(rgb)
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(value)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className="relative" ref={dropdownRef}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
      )}
      
      {/* Color Trigger Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between p-3 border border-gray-300 rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
      >
        <div className="flex items-center space-x-3">
          <div 
            className="w-6 h-6 rounded-md border border-gray-200 shadow-sm"
            style={{ backgroundColor: value }}
          />
          <span className="text-sm font-mono text-gray-700">{value.toUpperCase()}</span>
        </div>
        <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Color Picker Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4">
          {/* Tab Navigation */}
          <div className="flex space-x-1 mb-4 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('palette')}
              className={`flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                activeTab === 'palette' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Palette className="h-4 w-4 mr-2" />
              Palette
            </button>
            <button
              onClick={() => setActiveTab('custom')}
              className={`flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                activeTab === 'custom' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Custom
            </button>
          </div>

          {activeTab === 'palette' ? (
            /* Predefined Palette */
            <div className="space-y-4">
              <div className="grid grid-cols-8 gap-2">
                {colorPalette.map((color) => (
                  <button
                    key={color}
                    onClick={() => onChange(color)}
                    className={`w-8 h-8 rounded-lg border-2 transition-all duration-200 hover:scale-110 ${
                      value === color 
                        ? 'border-gray-400 ring-2 ring-blue-500 ring-offset-1' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>
            </div>
          ) : (
            /* Custom Color Controls */
            <div className="space-y-4">
              {/* Color Preview & Copy */}
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-10 h-10 rounded-lg border border-gray-200 shadow-sm"
                    style={{ backgroundColor: value }}
                  />
                  <div>
                    <div className="text-sm font-mono font-medium text-gray-900">{value.toUpperCase()}</div>
                    <div className="text-xs text-gray-500">
                      RGB({currentRgb.r}, {currentRgb.g}, {currentRgb.b})
                    </div>
                  </div>
                </div>
                <button
                  onClick={copyToClipboard}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                  title="Copy color code"
                >
                  {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                </button>
              </div>

              {/* RGB Sliders */}
              <div className="space-y-3">
                <div className="text-sm font-medium text-gray-700">RGB</div>
                {(['r', 'g', 'b'] as const).map((channel) => (
                  <div key={channel} className="flex items-center space-x-3">
                    <span className="w-4 text-xs font-medium text-gray-600 uppercase">{channel}</span>
                    <input
                      type="range"
                      min="0"
                      max="255"
                      value={currentRgb[channel]}
                      onChange={(e) => updateColor({ ...currentRgb, [channel]: parseInt(e.target.value) })}
                      className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      style={{
                        background: `linear-gradient(to right, 
                          ${rgbToHex(
                            channel === 'r' ? 0 : currentRgb.r,
                            channel === 'g' ? 0 : currentRgb.g,
                            channel === 'b' ? 0 : currentRgb.b
                          )}, 
                          ${rgbToHex(
                            channel === 'r' ? 255 : currentRgb.r,
                            channel === 'g' ? 255 : currentRgb.g,
                            channel === 'b' ? 255 : currentRgb.b
                          )})`
                      }}
                    />
                    <span className="w-8 text-xs text-gray-600">{currentRgb[channel]}</span>
                  </div>
                ))}
              </div>

              {/* HSL Sliders */}
              <div className="space-y-3">
                <div className="text-sm font-medium text-gray-700">HSL</div>
                <div className="flex items-center space-x-3">
                  <span className="w-4 text-xs font-medium text-gray-600">H</span>
                  <input
                    type="range"
                    min="0"
                    max="360"
                    value={currentHsl.h}
                    onChange={(e) => updateFromHsl({ ...currentHsl, h: parseInt(e.target.value) })}
                    className="flex-1 h-2 bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-cyan-500 via-blue-500 via-purple-500 to-red-500 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <span className="w-8 text-xs text-gray-600">{Math.round(currentHsl.h)}°</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="w-4 text-xs font-medium text-gray-600">S</span>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={currentHsl.s}
                    onChange={(e) => updateFromHsl({ ...currentHsl, s: parseInt(e.target.value) })}
                    className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <span className="w-8 text-xs text-gray-600">{Math.round(currentHsl.s)}%</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="w-4 text-xs font-medium text-gray-600">L</span>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={currentHsl.l}
                    onChange={(e) => updateFromHsl({ ...currentHsl, l: parseInt(e.target.value) })}
                    className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <span className="w-8 text-xs text-gray-600">{Math.round(currentHsl.l)}%</span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default ColorPicker
