import { useState, useEffect } from 'react'
import { ChevronDown, Calendar, Check, X, Plus, Settings } from 'lucide-react'
import {
  TextField,
  NumberField,
  SingleSelectField,
  MultiSelectField
} from './fieldTypes'

interface CustomField {
  id: string
  label: string
  key: string
  field_type: string
  resource_type: string
  namespace: string
  description?: string
  placeholder_text?: string
  help_text?: string
  is_required: boolean
  is_searchable: boolean
  is_filterable: boolean
  is_ai_enabled: boolean
  is_active: boolean
  field_options?: any
  validation_rules?: any
}

interface CustomFieldRendererProps {
  fields: CustomField[]
  values: { [key: string]: any }
  onChange: (key: string, value: any) => void
  resourceType: string
  showFieldManagement?: boolean
  onAddField?: () => void
  onFieldSettings?: (field: CustomField) => void
  onRemoveField?: (fieldId: string) => void
}

const CustomFieldRenderer: React.FC<CustomFieldRendererProps> = ({
  fields,
  values,
  onChange,
  resourceType,
  showFieldManagement = false,
  onAddField,
  onFieldSettings,
  onRemoveField
}) => {
  const [openDropdowns, setOpenDropdowns] = useState<{ [key: string]: boolean }>({})

  const toggleDropdown = (fieldKey: string) => {
    setOpenDropdowns(prev => ({
      ...prev,
      [fieldKey]: !prev[fieldKey]
    }))
  }

  const renderField = (field: CustomField) => {
    const value = values[field.key] || ''
    const fieldId = `custom_field_${field.key}`
    const hasError = field.is_required && !value
    const errorMessage = hasError ? `${field.label} is required` : undefined

    const commonProps = {
      id: fieldId,
      value,
      onChange: (newValue: any) => onChange(field.key, newValue),
      placeholder: field.placeholder_text || `Enter ${field.label.toLowerCase()}...`,
      required: field.is_required,
      error: errorMessage,
      validation: field.validation_rules
    }

    switch (field.field_type) {
      case 'text':
        return <TextField {...commonProps} />

      case 'number':
        return <NumberField {...commonProps} />

      case 'single_select':
        return (
          <SingleSelectField
            {...commonProps}
            options={field.field_options?.options || []}
          />
        )

      case 'multi_select':
        return (
          <MultiSelectField
            {...commonProps}
            options={field.field_options?.options || []}
          />
        )

      case 'textarea':
        return (
          <textarea
            id={fieldId}
            value={value}
            onChange={(e) => onChange(field.key, e.target.value)}
            placeholder={field.placeholder_text || `Enter ${field.label.toLowerCase()}...`}
            rows={3}
            className={`${baseClasses} ${errorClasses}`}
            required={field.is_required}
          />
        )

      case 'number':
        return (
          <input
            id={fieldId}
            type="number"
            value={value}
            onChange={(e) => onChange(field.key, parseFloat(e.target.value) || '')}
            placeholder={field.placeholder_text || '0'}
            className={`${baseClasses} ${errorClasses}`}
            required={field.is_required}
          />
        )

      case 'email':
        return (
          <input
            id={fieldId}
            type="email"
            value={value}
            onChange={(e) => onChange(field.key, e.target.value)}
            placeholder={field.placeholder_text || '<EMAIL>'}
            className={`${baseClasses} ${errorClasses}`}
            required={field.is_required}
          />
        )

      case 'url':
        return (
          <input
            id={fieldId}
            type="url"
            value={value}
            onChange={(e) => onChange(field.key, e.target.value)}
            placeholder={field.placeholder_text || 'https://example.com'}
            className={`${baseClasses} ${errorClasses}`}
            required={field.is_required}
          />
        )

      case 'date':
        return (
          <div className="relative">
            <input
              id={fieldId}
              type="date"
              value={value}
              onChange={(e) => onChange(field.key, e.target.value)}
              className={`${baseClasses} ${errorClasses}`}
              required={field.is_required}
            />
            <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>
        )

      case 'boolean':
        return (
          <div className="flex items-center">
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                id={fieldId}
                type="checkbox"
                checked={value === true || value === 'true'}
                onChange={(e) => onChange(field.key, e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              <span className="ml-3 text-sm text-gray-700">
                {value ? 'Yes' : 'No'}
              </span>
            </label>
          </div>
        )

      case 'single_select':
        const options = field.field_options?.options || []
        return (
          <div className="relative">
            <button
              type="button"
              onClick={() => toggleDropdown(field.key)}
              className={`${baseClasses} ${errorClasses} flex items-center justify-between`}
            >
              <span className={value ? 'text-gray-900' : 'text-gray-500'}>
                {value ? options.find((opt: any) => opt.value === value)?.label || value : `Select ${field.label.toLowerCase()}...`}
              </span>
              <ChevronDown className="h-4 w-4" />
            </button>
            
            {openDropdowns[field.key] && (
              <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg">
                {options.map((option: any) => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => {
                      onChange(field.key, option.value)
                      toggleDropdown(field.key)
                    }}
                    className="w-full text-left px-3 py-2 hover:bg-gray-50 first:rounded-t-md last:rounded-b-md flex items-center justify-between"
                  >
                    <span>{option.label}</span>
                    {value === option.value && <Check className="h-4 w-4 text-blue-600" />}
                  </button>
                ))}
              </div>
            )}
          </div>
        )

      case 'multi_select':
        const multiOptions = field.field_options?.options || []
        const selectedValues = Array.isArray(value) ? value : (value ? [value] : [])
        
        return (
          <div className="relative">
            <button
              type="button"
              onClick={() => toggleDropdown(field.key)}
              className={`${baseClasses} ${errorClasses} flex items-center justify-between min-h-[42px]`}
            >
              <div className="flex flex-wrap gap-1">
                {selectedValues.length > 0 ? (
                  selectedValues.map((val: string) => {
                    const option = multiOptions.find((opt: any) => opt.value === val)
                    return (
                      <span
                        key={val}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
                      >
                        {option?.label || val}
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation()
                            const newValues = selectedValues.filter((v: string) => v !== val)
                            onChange(field.key, newValues)
                          }}
                          className="ml-1 hover:text-blue-600"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    )
                  })
                ) : (
                  <span className="text-gray-500">Select {field.label.toLowerCase()}...</span>
                )}
              </div>
              <ChevronDown className="h-4 w-4 flex-shrink-0" />
            </button>
            
            {openDropdowns[field.key] && (
              <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                {multiOptions.map((option: any) => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => {
                      const isSelected = selectedValues.includes(option.value)
                      const newValues = isSelected
                        ? selectedValues.filter((v: string) => v !== option.value)
                        : [...selectedValues, option.value]
                      onChange(field.key, newValues)
                    }}
                    className="w-full text-left px-3 py-2 hover:bg-gray-50 first:rounded-t-md last:rounded-b-md flex items-center justify-between"
                  >
                    <span>{option.label}</span>
                    {selectedValues.includes(option.value) && <Check className="h-4 w-4 text-blue-600" />}
                  </button>
                ))}
              </div>
            )}
          </div>
        )

      default:
        return (
          <input
            id={fieldId}
            type="text"
            value={value}
            onChange={(e) => onChange(field.key, e.target.value)}
            placeholder={field.placeholder_text || `Enter ${field.label.toLowerCase()}...`}
            className={`${baseClasses} ${errorClasses}`}
            required={field.is_required}
          />
        )
    }
  }

  // Filter fields for the current resource type
  const relevantFields = fields.filter(field => 
    field.resource_type === resourceType || field.resource_type === 'global'
  ).filter(field => field.is_active)

  if (relevantFields.length === 0 && !showFieldManagement) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* Custom Fields Header */}
      {(relevantFields.length > 0 || showFieldManagement) && (
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Custom Fields</h3>
            <p className="text-sm text-gray-500">Additional information for this {resourceType.slice(0, -1)}</p>
          </div>
          {showFieldManagement && (
            <button
              type="button"
              onClick={onAddField}
              className="btn btn-secondary flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Field
            </button>
          )}
        </div>
      )}

      {/* Custom Fields */}
      {relevantFields.map((field) => (
        <div key={field.id} className="space-y-2">
          <div className="flex items-center justify-between">
            <label
              htmlFor={`custom_field_${field.key}`}
              className="block text-sm font-medium text-gray-700"
            >
              {field.label}
              {field.is_required && <span className="text-red-500 ml-1">*</span>}
            </label>
            {showFieldManagement && (
              <div className="flex items-center space-x-2">
                <button
                  type="button"
                  onClick={() => onFieldSettings?.(field)}
                  className="text-gray-400 hover:text-gray-600 p-1 rounded"
                  title="Field Settings"
                >
                  <Settings className="h-4 w-4" />
                </button>
                <button
                  type="button"
                  onClick={() => onRemoveField?.(field.id)}
                  className="text-red-400 hover:text-red-600 p-1 rounded"
                  title="Remove Field"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
          
          {renderField(field)}
          
          {field.help_text && (
            <p className="text-xs text-gray-500">{field.help_text}</p>
          )}
          
          {field.is_required && !values[field.key] && (
            <p className="text-xs text-red-500">This field is required</p>
          )}
        </div>
      ))}

      {/* No fields message */}
      {relevantFields.length === 0 && showFieldManagement && (
        <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
          <div className="flex flex-col items-center">
            <Settings className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No custom fields</h3>
            <p className="text-gray-500 mb-4">Add custom fields to collect additional information</p>
            <button
              type="button"
              onClick={onAddField}
              className="btn btn-primary flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Field
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default CustomFieldRenderer
