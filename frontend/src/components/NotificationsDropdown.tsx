import React, { useState, useRef, useEffect } from 'react'
import { Bell } from 'lucide-react'

interface Notification {
  id: string
  title: string
  time: string
  isRead: boolean
}

const NotificationsDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const notifications: Notification[] = [
    {
      id: '1',
      title: 'New order #ORD-1001 has been placed',
      time: '10 minutes ago',
      isRead: false
    },
    {
      id: '2',
      title: 'New order #ORD-1002 has been placed',
      time: '10 minutes ago',
      isRead: false
    },
    {
      id: '3',
      title: 'New order #ORD-1003 has been placed',
      time: '10 minutes ago',
      isRead: false
    }
  ]

  const unreadCount = notifications.filter(n => !n.isRead).length

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="text-gray-600 hover:text-gray-800 relative transition-colors p-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}
        aria-expanded={isOpen}
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
            <span className="text-xs text-white font-medium">{unreadCount}</span>
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 animate-in fade-in-0 zoom-in-95 duration-200">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Notifications</h3>
          </div>

          <div className="max-h-96 overflow-y-auto">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className="p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200 last:border-b-0"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 mb-1">
                      {notification.title}
                    </p>
                    <p className="text-xs text-gray-500">{notification.time}</p>
                  </div>
                  {!notification.isRead && (
                    <div className="w-2 h-2 bg-black rounded-full ml-3 mt-2 flex-shrink-0"></div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="p-4 border-t border-gray-200">
            <button className="w-full text-center text-sm font-medium text-gray-600 hover:text-gray-800 transition-colors">
              View all notifications
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default NotificationsDropdown
