import { useState, useRef, useEffect } from 'react'
import { ChevronDown, Check, Building2, Users, Settings } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import CustomSelect from './CustomSelect'
import { Organization, Workspace } from '../types/auth'

interface WorkspaceSwitcherProps {
  className?: string
}

const WorkspaceSwitcher = ({ className = '' }: WorkspaceSwitcherProps) => {
  const {
    currentOrganization,
    currentWorkspace,
    availableOrganizations,
    availableWorkspaces,
    switchWorkspace,
    isLoading
  } = useAuth()

  const navigate = useNavigate()
  const [isOpen, setIsOpen] = useState(false)
  const [selectedOrgId, setSelectedOrgId] = useState(currentOrganization?.id || '')
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Update selected org when current organization changes
  useEffect(() => {
    if (currentOrganization) {
      setSelectedOrgId(currentOrganization.id)
    }
  }, [currentOrganization])

  const handleWorkspaceSwitch = (organizationId: string, workspaceId: string) => {
    switchWorkspace(organizationId, workspaceId)
    setIsOpen(false)
  }

  const getWorkspacesForOrg = (orgId: string): Workspace[] => {
    return availableWorkspaces.filter(ws => ws.organizationId === orgId)
  }

  const getOrganizationById = (orgId: string): Organization | undefined => {
    return availableOrganizations.find(org => org.id === orgId)
  }

  if (isLoading || !currentOrganization || !currentWorkspace) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-10 bg-gray-200 rounded-lg w-48"></div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Current Workspace Display */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors duration-200 min-w-[240px] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label="Switch workspace"
        aria-expanded={isOpen}
      >
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {/* Organization Icon - Blue square with building icon */}
          <div className="w-6 h-6 bg-blue-600 rounded-lg flex items-center justify-center ring-2 ring-white shadow-sm">
            <Building2 className="h-4 w-4 text-white" />
          </div>

          {/* Organization Info - Only name, no subtitle */}
          <div className="flex-1 min-w-0 text-left">
            <div className="text-sm font-semibold text-gray-900 truncate">
              {currentOrganization.name}
            </div>
          </div>
        </div>

        <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${
          isOpen ? 'transform rotate-180' : ''
        }`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 max-h-96 overflow-y-auto animate-in fade-in-0 zoom-in-95 duration-200">
          {/* Organization Selector */}
          {availableOrganizations.length > 1 && (
            <div className="p-3 border-b border-gray-100">
              <label className="block text-xs font-medium text-gray-700 mb-2">
                Organization
              </label>
              <CustomSelect
                options={availableOrganizations.map(org => ({
                  value: org.id,
                  label: org.name
                }))}
                value={selectedOrgId}
                onChange={setSelectedOrgId}
                size="sm"
              />
            </div>
          )}

          {/* Workspace List */}
          <div className="py-2">
            <div className="px-3 py-1">
              <div className="text-xs font-medium text-gray-700 mb-2">
                Workspaces
              </div>
            </div>

            {getWorkspacesForOrg(selectedOrgId).map((workspace) => {
              const isCurrentWorkspace = workspace.id === currentWorkspace.id
              const organization = getOrganizationById(workspace.organizationId)

              return (
                <button
                  key={workspace.id}
                  onClick={() => handleWorkspaceSwitch(workspace.organizationId, workspace.id)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-50 transition-colors duration-200 ${
                    isCurrentWorkspace ? 'bg-blue-50' : ''
                  }`}
                >
                  {/* Workspace Icon */}
                  <div
                    className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium flex-shrink-0"
                    style={{ backgroundColor: workspace.color }}
                  >
                    {workspace.icon || workspace.name.charAt(0).toUpperCase()}
                  </div>

                  {/* Workspace Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-medium truncate ${
                        isCurrentWorkspace ? 'text-blue-900' : 'text-gray-900'
                      }`}>
                        {workspace.name}
                      </span>
                      {isCurrentWorkspace && (
                        <Check className="h-4 w-4 text-blue-600 flex-shrink-0" />
                      )}
                    </div>
                    {workspace.description && (
                      <div className="text-xs text-gray-500 truncate">
                        {workspace.description}
                      </div>
                    )}
                    {organization && workspace.organizationId !== selectedOrgId && (
                      <div className="text-xs text-gray-400 truncate">
                        {organization.name}
                      </div>
                    )}
                  </div>

                  {/* Workspace Stats */}
                  <div className="flex items-center space-x-1 text-xs text-gray-400 flex-shrink-0">
                    <Users className="h-3 w-3" />
                    <span>{workspace.usage.integrationCount}</span>
                  </div>
                </button>
              )
            })}
          </div>

          {/* Footer Actions */}
          <div className="border-t border-gray-100 p-2">
            <button
              onClick={() => {
                navigate('/settings')
                setIsOpen(false)
              }}
              className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md transition-colors duration-200"
            >
              <Building2 className="h-4 w-4" />
              <span>Create Workspace</span>
            </button>
            <button
              onClick={() => {
                navigate('/settings')
                setIsOpen(false)
              }}
              className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md transition-colors duration-200"
            >
              <Settings className="h-4 w-4" />
              <span>Workspace Settings</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default WorkspaceSwitcher
