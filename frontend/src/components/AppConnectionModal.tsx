import { useState } from 'react'
import { X, Eye, EyeOff, ExternalLink, CheckCircle, AlertCircle, Info } from 'lucide-react'
import { mockApiService } from '../services/mockApi'
import toast from 'react-hot-toast'

import { App } from '../data/mockData'

interface AppConnectionModalProps {
  isOpen: boolean
  onClose: () => void
  app: App
  onConnect: (credentials: any) => void
}

const AppConnectionModal = ({ isOpen, onClose, app, onConnect }: AppConnectionModalProps) => {
  const [step, setStep] = useState(1)
  const [showApiKey, setShowApiKey] = useState(false)
  const [showApiSecret, setShowApiSecret] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle')

  const [credentials, setCredentials] = useState({
    apiKey: '',
    apiSecret: '',
    storeUrl: '',
    accessToken: ''
  })

  if (!isOpen) return null

  const Icon = typeof app.icon === 'string' ? () => <div className="text-center">{String(app.icon)}</div> : app.icon

  const handleConnect = async () => {
    setIsConnecting(true)
    setConnectionStatus('testing')

    try {
      let validationResult

      if (app.id === 'shopify') {
        validationResult = await mockApiService.shopify.validateCredentials(
          credentials.storeUrl,
          credentials.accessToken
        )
      } else if (app.id === 'bigcommerce') {
        validationResult = await mockApiService.bigcommerce.validateCredentials(
          credentials.storeUrl,
          credentials.apiKey,
          credentials.apiSecret
        )
      } else {
        // Generic validation
        validationResult = { valid: Math.random() > 0.2 }
      }

      if (validationResult.valid) {
        setConnectionStatus('success')

        // Connect the app
        const connectResult = await mockApiService.apps.connect(app.id, {
          ...credentials,
          storeInfo: validationResult.storeInfo
        })

        if (connectResult.success) {
          toast.success(connectResult.message)
          setTimeout(() => {
            onConnect(credentials)
            onClose()
            resetModal()
          }, 2000)
        } else {
          setConnectionStatus('error')
          setIsConnecting(false)
          toast.error(connectResult.message)
        }
      } else {
        setConnectionStatus('error')
        setIsConnecting(false)
        toast.error('Invalid credentials. Please check your information and try again.')
      }
    } catch (error) {
      setConnectionStatus('error')
      setIsConnecting(false)
      toast.error('Connection failed. Please try again.')
    }
  }

  const resetModal = () => {
    setStep(1)
    setCredentials({ apiKey: '', apiSecret: '', storeUrl: '', accessToken: '' })
    setConnectionStatus('idle')
    setIsConnecting(false)
  }

  const getShopifyFields = () => (
    <>
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
        <div className="flex">
          <Info className="h-5 w-5 text-green-400 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-green-900">How to get your Shopify credentials:</h4>
            <ol className="text-sm text-green-800 mt-2 space-y-1 list-decimal list-inside">
              <li>Go to your Shopify admin panel</li>
              <li>Navigate to Apps → Manage private apps</li>
              <li>Create a private app or use existing one</li>
              <li>Enable Admin API access and copy the access token</li>
            </ol>
            <div className="mt-2">
              <a
                href="https://help.shopify.com/en/manual/apps/private-apps"
                target="_blank"
                rel="noopener noreferrer"
                className="text-green-700 hover:text-green-800 text-sm inline-flex items-center"
              >
                Learn more about private apps
                <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </div>
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Store URL *
        </label>
        <div className="relative">
          <input
            type="text"
            value={credentials.storeUrl}
            onChange={(e) => setCredentials({ ...credentials, storeUrl: e.target.value })}
            className="input pr-20"
            placeholder="your-store"
            required
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <span className="text-gray-500 text-sm">.myshopify.com</span>
          </div>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          Your Shopify store name (without .myshopify.com)
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Private App Access Token *
        </label>
        <div className="relative">
          <input
            type={showApiKey ? 'text' : 'password'}
            value={credentials.accessToken}
            onChange={(e) => setCredentials({ ...credentials, accessToken: e.target.value })}
            className="input pr-10"
            placeholder="shpat_..."
            required
          />
          <button
            type="button"
            onClick={() => setShowApiKey(!showApiKey)}
            className="absolute inset-y-0 right-0 flex items-center pr-3"
          >
            {showApiKey ? <EyeOff className="h-4 w-4 text-gray-400" /> : <Eye className="h-4 w-4 text-gray-400" />}
          </button>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          The access token from your private app (starts with "shpat_")
        </p>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
        <div className="flex">
          <AlertCircle className="h-4 w-4 text-yellow-400 mt-0.5" />
          <div className="ml-2">
            <p className="text-xs text-yellow-800">
              <strong>Required permissions:</strong> Make sure your private app has read access to products, orders, and customers.
            </p>
          </div>
        </div>
      </div>
    </>
  )

  const getBigCommerceFields = () => (
    <>
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div className="flex">
          <Info className="h-5 w-5 text-blue-400 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-900">How to get your BigCommerce credentials:</h4>
            <ol className="text-sm text-blue-800 mt-2 space-y-1 list-decimal list-inside">
              <li>Go to your BigCommerce admin panel</li>
              <li>Navigate to Advanced Settings → API Accounts</li>
              <li>Create a new API account or use existing one</li>
              <li>Copy the Store Hash, API Token, and Client ID</li>
            </ol>
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Store Hash *
        </label>
        <input
          type="text"
          value={credentials.storeUrl}
          onChange={(e) => setCredentials({ ...credentials, storeUrl: e.target.value })}
          className="input"
          placeholder="abc123def"
          required
        />
        <p className="text-xs text-gray-500 mt-1">
          Found in your store URL: store-<strong>abc123def</strong>.mybigcommerce.com
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          API Token *
        </label>
        <div className="relative">
          <input
            type={showApiKey ? 'text' : 'password'}
            value={credentials.apiKey}
            onChange={(e) => setCredentials({ ...credentials, apiKey: e.target.value })}
            className="input pr-10"
            placeholder="Your API access token"
            required
          />
          <button
            type="button"
            onClick={() => setShowApiKey(!showApiKey)}
            className="absolute inset-y-0 right-0 flex items-center pr-3"
          >
            {showApiKey ? <EyeOff className="h-4 w-4 text-gray-400" /> : <Eye className="h-4 w-4 text-gray-400" />}
          </button>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          The API access token from your BigCommerce API account
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Client ID *
        </label>
        <div className="relative">
          <input
            type={showApiSecret ? 'text' : 'password'}
            value={credentials.apiSecret}
            onChange={(e) => setCredentials({ ...credentials, apiSecret: e.target.value })}
            className="input pr-10"
            placeholder="Your client ID"
            required
          />
          <button
            type="button"
            onClick={() => setShowApiSecret(!showApiSecret)}
            className="absolute inset-y-0 right-0 flex items-center pr-3"
          >
            {showApiSecret ? <EyeOff className="h-4 w-4 text-gray-400" /> : <Eye className="h-4 w-4 text-gray-400" />}
          </button>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          The client ID associated with your API account
        </p>
      </div>
    </>
  )

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Icon className="mx-auto h-16 w-16 text-gray-600" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">Connect to {app.name}</h3>
        <p className="mt-2 text-sm text-gray-600">{app.description}</p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">Before you start:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Make sure you have admin access to your {app.name} store</li>
          <li>• Have your API credentials ready</li>
          <li>• Ensure your store is active and accessible</li>
        </ul>
      </div>

      <div className="flex justify-between">
        <button
          onClick={onClose}
          className="btn btn-secondary"
        >
          Cancel
        </button>
        <button
          onClick={() => setStep(2)}
          className="btn btn-primary"
        >
          Continue
        </button>
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Enter your {app.name} credentials</h3>
        <p className="mt-1 text-sm text-gray-600">
          We'll use these to securely connect to your store
        </p>
      </div>

      <div className="space-y-4">
        {app.id === 'shopify' ? getShopifyFields() : getBigCommerceFields()}
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-yellow-400" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-yellow-800">Security Note</h4>
            <p className="text-sm text-yellow-700 mt-1">
              Your credentials are encrypted and stored securely. We never share your data with third parties.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          onClick={() => setStep(1)}
          className="btn btn-secondary"
        >
          Back
        </button>
        <button
          onClick={handleConnect}
          disabled={isConnecting || !credentials.apiKey || !credentials.storeUrl}
          className="btn btn-primary"
        >
          {isConnecting ? 'Testing Connection...' : 'Test & Connect'}
        </button>
      </div>
    </div>
  )

  const renderConnectionStatus = () => (
    <div className="space-y-6 text-center">
      {connectionStatus === 'testing' && (
        <>
          <div className="mx-auto h-16 w-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
          <div>
            <h3 className="text-lg font-medium text-gray-900">Testing Connection</h3>
            <p className="mt-2 text-sm text-gray-600">
              We're verifying your credentials and testing the connection...
            </p>
          </div>
        </>
      )}

      {connectionStatus === 'success' && (
        <>
          <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
          <div>
            <h3 className="text-lg font-medium text-gray-900">Connection Successful!</h3>
            <p className="mt-2 text-sm text-gray-600">
              Your {app.name} store has been connected successfully. You can now start syncing data.
            </p>
          </div>
        </>
      )}

      {connectionStatus === 'error' && (
        <>
          <X className="mx-auto h-16 w-16 text-red-500" />
          <div>
            <h3 className="text-lg font-medium text-gray-900">Connection Failed</h3>
            <p className="mt-2 text-sm text-gray-600">
              We couldn't connect to your {app.name} store. Please check your credentials and try again.
            </p>
          </div>
          <button
            onClick={() => {
              setConnectionStatus('idle')
              setStep(2)
            }}
            className="btn btn-primary"
          >
            Try Again
          </button>
        </>
      )}
    </div>
  )

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {connectionStatus === 'idle' ? `Connect ${app.name}` : 'Connection Status'}
          </h2>
          <button
            onClick={() => {
              onClose()
              resetModal()
            }}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          {connectionStatus === 'idle' ? (
            step === 1 ? renderStep1() : renderStep2()
          ) : (
            renderConnectionStatus()
          )}
        </div>
      </div>
    </div>
  )
}

export default AppConnectionModal
