import { useState } from 'react'
import {
  X,
  Settings,
  Database,
  Clock,
  Shield,
  AlertCircle,
  CheckCircle,
  Save,
  TestTube,
  Trash2
} from 'lucide-react'
import CustomSelect from './CustomSelect'

interface AppConfigModalProps {
  isOpen: boolean
  onClose: () => void
  app: {
    id: string
    name: string
    description: string
    icon: React.ComponentType<any>
    isConnected: boolean
    lastSync?: string
  }
  onSave: (config: any) => void
  onDisconnect: () => void
}

const AppConfigModal = ({ isOpen, onClose, app, onSave, onDisconnect }: AppConfigModalProps) => {
  const [activeTab, setActiveTab] = useState('general')
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null)

  const [config, setConfig] = useState({
    // General settings
    enabled: true,
    name: app.name,
    description: app.description,

    // Sync settings
    syncFrequency: 'hourly',
    syncProducts: true,
    syncOrders: true,
    syncCustomers: true,
    syncInventory: false,

    // Data mapping
    productMapping: {
      title: 'name',
      description: 'description',
      price: 'price',
      sku: 'sku'
    },

    // Advanced settings
    batchSize: 100,
    retryAttempts: 3,
    timeout: 30,
    webhookUrl: '',

    // Security
    encryptData: true,
    logLevel: 'info'
  })

  if (!isOpen) return null

  const Icon = app.icon

  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'sync', name: 'Sync Settings', icon: Database },
    { id: 'schedule', name: 'Schedule', icon: Clock },
    { id: 'security', name: 'Security', icon: Shield },
  ]

  const handleTestConnection = async () => {
    setIsTesting(true)
    setTestResult(null)

    // Simulate API test
    setTimeout(() => {
      const success = Math.random() > 0.2 // 80% success rate
      setTestResult(success ? 'success' : 'error')
      setIsTesting(false)
    }, 2000)
  }

  const handleSave = () => {
    onSave(config)
    onClose()
  }

  const renderGeneralTab = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Connection Name
        </label>
        <input
          type="text"
          value={config.name}
          onChange={(e) => setConfig({ ...config, name: e.target.value })}
          className="input"
          placeholder="Enter connection name"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <textarea
          value={config.description}
          onChange={(e) => setConfig({ ...config, description: e.target.value })}
          className="input"
          rows={3}
          placeholder="Describe this connection"
        />
      </div>

      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-sm font-medium text-gray-900">Enable Connection</h4>
          <p className="text-sm text-gray-500">Allow this app to sync data</p>
        </div>
        <button
          onClick={() => setConfig({ ...config, enabled: !config.enabled })}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            config.enabled ? 'bg-primary-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              config.enabled ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      <div className="border-t pt-6">
        <h4 className="text-sm font-medium text-gray-900 mb-4">Connection Test</h4>
        <div className="flex items-center space-x-4">
          <button
            onClick={handleTestConnection}
            disabled={isTesting}
            className="btn btn-secondary flex items-center"
          >
            <TestTube className="h-4 w-4 mr-2" />
            {isTesting ? 'Testing...' : 'Test Connection'}
          </button>

          {testResult && (
            <div className={`flex items-center ${
              testResult === 'success' ? 'text-green-600' : 'text-red-600'
            }`}>
              {testResult === 'success' ? (
                <CheckCircle className="h-4 w-4 mr-1" />
              ) : (
                <AlertCircle className="h-4 w-4 mr-1" />
              )}
              <span className="text-sm">
                {testResult === 'success' ? 'Connection successful' : 'Connection failed'}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )

  const renderSyncTab = () => (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-4">Data Types to Sync</h4>
        <div className="space-y-3">
          {[
            { key: 'syncProducts', label: 'Products', description: 'Product catalog and details' },
            { key: 'syncOrders', label: 'Orders', description: 'Order history and status' },
            { key: 'syncCustomers', label: 'Customers', description: 'Customer information' },
            { key: 'syncInventory', label: 'Inventory', description: 'Stock levels and tracking' }
          ].map((item) => (
            <div key={item.key} className="flex items-center justify-between">
              <div>
                <h5 className="text-sm font-medium text-gray-900">{item.label}</h5>
                <p className="text-sm text-gray-500">{item.description}</p>
              </div>
              <button
                onClick={() => setConfig({ ...config, [item.key]: !config[item.key as keyof typeof config] })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  config[item.key as keyof typeof config] ? 'bg-primary-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    config[item.key as keyof typeof config] ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Batch Size
        </label>
        <CustomSelect
          options={[
            { value: '50', label: '50 records' },
            { value: '100', label: '100 records' },
            { value: '250', label: '250 records' },
            { value: '500', label: '500 records' }
          ]}
          value={config.batchSize.toString()}
          onChange={(value) => setConfig({ ...config, batchSize: parseInt(value) })}
        />
        <p className="text-xs text-gray-500 mt-1">Number of records to process in each batch</p>
      </div>
    </div>
  )

  const renderScheduleTab = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Sync Frequency
        </label>
        <CustomSelect
          options={[
            { value: 'manual', label: 'Manual only' },
            { value: '15min', label: 'Every 15 minutes' },
            { value: '30min', label: 'Every 30 minutes' },
            { value: 'hourly', label: 'Every hour' },
            { value: 'daily', label: 'Daily' },
            { value: 'weekly', label: 'Weekly' }
          ]}
          value={config.syncFrequency}
          onChange={(value) => setConfig({ ...config, syncFrequency: value })}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Retry Attempts
        </label>
        <input
          type="number"
          min="1"
          max="10"
          value={config.retryAttempts}
          onChange={(e) => setConfig({ ...config, retryAttempts: parseInt(e.target.value) })}
          className="input"
        />
        <p className="text-xs text-gray-500 mt-1">Number of retry attempts on failure</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Timeout (seconds)
        </label>
        <input
          type="number"
          min="10"
          max="300"
          value={config.timeout}
          onChange={(e) => setConfig({ ...config, timeout: parseInt(e.target.value) })}
          className="input"
        />
      </div>
    </div>
  )

  const renderSecurityTab = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-sm font-medium text-gray-900">Encrypt Data</h4>
          <p className="text-sm text-gray-500">Encrypt synced data at rest</p>
        </div>
        <button
          onClick={() => setConfig({ ...config, encryptData: !config.encryptData })}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            config.encryptData ? 'bg-primary-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              config.encryptData ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Log Level
        </label>
        <CustomSelect
          options={[
            { value: 'error', label: 'Error only' },
            { value: 'warn', label: 'Warning' },
            { value: 'info', label: 'Info' },
            { value: 'debug', label: 'Debug' }
          ]}
          value={config.logLevel}
          onChange={(value) => setConfig({ ...config, logLevel: value })}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Webhook URL (Optional)
        </label>
        <input
          type="url"
          value={config.webhookUrl}
          onChange={(e) => setConfig({ ...config, webhookUrl: e.target.value })}
          className="input"
          placeholder="https://your-app.com/webhook"
        />
        <p className="text-xs text-gray-500 mt-1">Receive notifications about sync events</p>
      </div>

      <div className="border-t pt-6">
        <h4 className="text-sm font-medium text-gray-900 mb-4 text-red-600">Danger Zone</h4>
        <button
          onClick={onDisconnect}
          className="btn bg-red-600 text-white hover:bg-red-700 flex items-center"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Disconnect App
        </button>
        <p className="text-xs text-gray-500 mt-2">
          This will stop all syncing and remove the connection. This action cannot be undone.
        </p>
      </div>
    </div>
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralTab()
      case 'sync':
        return renderSyncTab()
      case 'schedule':
        return renderScheduleTab()
      case 'security':
        return renderSecurityTab()
      default:
        return renderGeneralTab()
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Icon className="h-8 w-8 text-gray-600 mr-3" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Configure {app.name}</h2>
              <p className="text-sm text-gray-500">Manage your {app.name} integration settings</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-gray-50 border-r border-gray-200">
            <nav className="p-4 space-y-1">
              {tabs.map((tab) => {
                const TabIcon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                  >
                    <TabIcon className="h-4 w-4 mr-3" />
                    {tab.name}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
            {renderTabContent()}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-500">
            Last synced: {app.lastSync || 'Never'}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="btn btn-secondary"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="btn btn-primary flex items-center"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AppConfigModal
