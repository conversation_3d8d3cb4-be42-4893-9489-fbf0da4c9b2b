import { Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { AuthProvider } from './contexts/AuthContext'
import { SettingsProvider } from './contexts/SettingsContext'
import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import AppsPage from './pages/AppsPage'
import ModernProductsPage from './pages/ModernProductsPage'
import CreateProductPage from './pages/CreateProductPage'
import PriceListsPage from './pages/PriceListsPage'
import MarkupPage from './pages/MarkupPage'
import InboundSharingPage from './pages/InboundSharingPage'
import OutboundSharingPage from './pages/OutboundSharingPage'
import VendorNetworkPage from './pages/VendorNetworkPage'
import OrdersPage from './pages/OrdersPage'
import OrderDetailsPage from './pages/OrderDetailsPageNew'
import CustomersPage from './pages/CustomersPage'
import CustomerDetailsPage from './pages/CustomerDetailsPage'
import MyOrganizationPage from './pages/MyOrganizationPage'
import UserDetailPage from './pages/UserDetailPage'
import SettingsPage from './pages/SettingsPage'
import AdminShopAsCustomerPage from './pages/AdminShopAsCustomerPage'
import WhatsAppPage from './pages/WhatsAppPage'
import NotFoundPage from './pages/NotFoundPage'

function App() {
  return (
    <AuthProvider>
      <SettingsProvider>
        <Layout>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/apps" element={<AppsPage />} />
            <Route path="/apps/whatsapp" element={<WhatsAppPage />} />
            <Route path="/orders" element={<OrdersPage />} />
            <Route path="/orders/:id" element={<OrderDetailsPage />} />
            <Route path="/products" element={<ModernProductsPage />} />
            <Route path="/products/create" element={<CreateProductPage />} />
            <Route path="/products/edit/:id" element={<CreateProductPage />} />
            <Route path="/products/price-lists" element={<PriceListsPage />} />
            <Route path="/products/markup" element={<MarkupPage />} />
            <Route path="/products/shared/inbound" element={<InboundSharingPage />} />
            <Route path="/products/shared/outbound" element={<OutboundSharingPage />} />
            <Route path="/products/shared/vendors" element={<VendorNetworkPage />} />
            <Route path="/customers" element={<CustomersPage />} />
            <Route path="/customers/:id" element={<CustomerDetailsPage />} />
            <Route path="/customers/:id/edit" element={<CustomerDetailsPage />} />
            <Route path="/customers/new" element={<CustomerDetailsPage />} />
            <Route path="/admin/shop-as-customer/:customerId" element={<AdminShopAsCustomerPage />} />
            <Route path="/storefront" element={<HomePage />} />
            <Route path="/sales" element={<HomePage />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="/settings/general" element={<SettingsPage />} />
            <Route path="/settings/jobs" element={<SettingsPage />} />
            <Route path="/settings/users" element={<Navigate to="/settings/users/my-organization/users" replace />} />
            <Route path="/settings/users/my-organization" element={<MyOrganizationPage />} />
            <Route path="/settings/users/my-organization/users" element={<MyOrganizationPage />} />
            <Route path="/settings/users/my-organization/users/:userId" element={<UserDetailPage />} />
            <Route path="/settings/users/my-organization/roles" element={<MyOrganizationPage />} />
            <Route path="/settings/users/my-organization/permissions" element={<Navigate to="/settings/users/my-organization/roles" replace />} />
            <Route path="/settings/users/guest-organization" element={<MyOrganizationPage />} />
            <Route path="/settings/users/guest-organization/users" element={<MyOrganizationPage />} />
            <Route path="/settings/users/guest-organization/users/:userId" element={<UserDetailPage />} />
            <Route path="/settings/security" element={<SettingsPage />} />
            <Route path="/settings/notifications" element={<SettingsPage />} />
            <Route path="/settings/database" element={<SettingsPage />} />
            <Route path="/users/my-organization" element={<Navigate to="/settings/users/my-organization/users" replace />} />
            <Route path="/users/my-organization/users" element={<Navigate to="/settings/users/my-organization/users" replace />} />
            <Route path="/users/my-organization/users/:userId" element={<Navigate to="/settings/users/my-organization/users/:userId" replace />} />
            <Route path="/users/my-organization/roles" element={<Navigate to="/settings/users/my-organization/roles" replace />} />
            <Route path="/users/guest-organization" element={<Navigate to="/settings/users/guest-organization" replace />} />
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Layout>
      </SettingsProvider>
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#10B981',
              secondary: '#fff',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: '#EF4444',
              secondary: '#fff',
            },
          },
        }}
      />
    </AuthProvider>
  )
}

export default App
