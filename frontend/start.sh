#!/bin/bash

echo "🚀 Starting Nucleux Frontend..."
echo "📁 Current directory: $(pwd)"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found!"
    echo "Please run this script from the frontend directory"
    exit 1
fi

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js not installed!"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Error: npm install failed!"
    exit 1
fi

echo "✅ Dependencies installed successfully!"

# Start development server
echo "🌐 Starting development server..."
echo "🔗 Open http://localhost:5173 in your browser"
echo "🔑 Login with: <EMAIL> / password"
echo "📋 Test order details: http://localhost:5173/orders/ORD-023"
echo ""

npm run dev
