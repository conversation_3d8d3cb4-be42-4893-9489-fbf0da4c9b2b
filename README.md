# Nucleux

A full-stack application built with Rust backend and React frontend.

## Tech Stack

### Backend
- **Rust** with Axum web framework
- **PostgreSQL** for relational data
- **MongoDB** for document storage
- **Redis** for caching (optional)
- **SQLx** for PostgreSQL operations
- **MongoDB Driver** for document operations

### Frontend
- **React** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **Axios** for API calls

## Project Structure

```
Nucleux/
├── backend/          # Rust API server
├── frontend/         # React application
└── docker-compose.yml # Database services
```

## Quick Start

### Prerequisites
- Rust (latest stable)
- Node.js (18+)
- Docker & Docker Compose

### Setup

1. **Start databases:**
   ```bash
   docker-compose up -d
   ```

2. **Backend setup:**
   ```bash
   cd backend
   cp .env.example .env
   cargo run
   ```

3. **Frontend setup:**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

### Development URLs
- Frontend: http://localhost:5173
- Backend API: http://localhost:8000
- PostgreSQL: localhost:5432
- MongoDB: localhost:27017
- Redis: localhost:6379

## Environment Variables

See `backend/.env.example` for required environment variables.

## API Documentation

API documentation will be available at http://localhost:8000/docs when the backend is running.
