version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: nucleux_postgres
    environment:
      POSTGRES_DB: nucleux
      POSTGRES_USER: nucleux_user
      POSTGRES_PASSWORD: nucleux_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    networks:
      - nucleux_network

  mongodb:
    image: mongo:7
    container_name: nucleux_mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: nucleux_user
      MONGO_INITDB_ROOT_PASSWORD: nucleux_password
      MONGO_INITDB_DATABASE: nucleux
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - nucleux_network

  redis:
    image: redis:7-alpine
    container_name: nucleux_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - nucleux_network
    command: redis-server --appendonly yes

volumes:
  postgres_data:
  mongodb_data:
  redis_data:

networks:
  nucleux_network:
    driver: bridge
