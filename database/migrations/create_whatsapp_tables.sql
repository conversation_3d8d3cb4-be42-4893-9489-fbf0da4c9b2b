-- WhatsApp Integration Database Schema

-- WhatsApp Accounts Table
CREATE TABLE whatsapp_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    profile_picture_url TEXT,
    qr_code TEXT, -- For storing QR code data during setup
    session_data JSONB, -- Store Selenium session data
    status VARCHAR(20) DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'connecting', 'error')),
    is_active BOOLEAN DEFAULT true,
    last_seen TIMESTAMP WITH TIME ZONE,
    webhook_url TEXT,
    api_token VARCHAR(255),
    settings JSONB DEFAULT '{}', -- Store account-specific settings
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    organization_id UUID REFERENCES organizations(id)
);

-- WhatsApp Contacts Table
CREATE TABLE whatsapp_contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    phone VARCHAR(20) NOT NULL,
    name VARCHAR(255),
    profile_picture_url TEXT,
    is_business BOOLEAN DEFAULT false,
    is_blocked BOOLEAN DEFAULT false,
    labels TEXT[], -- Array of labels/tags
    custom_fields JSONB DEFAULT '{}',
    last_seen TIMESTAMP WITH TIME ZONE,
    is_online BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(account_id, phone)
);

-- WhatsApp Conversations Table
CREATE TABLE whatsapp_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    contact_id UUID NOT NULL REFERENCES whatsapp_contacts(id) ON DELETE CASCADE,
    is_group BOOLEAN DEFAULT false,
    group_name VARCHAR(255),
    group_description TEXT,
    group_participants JSONB DEFAULT '[]', -- Array of participant phone numbers
    is_pinned BOOLEAN DEFAULT false,
    is_archived BOOLEAN DEFAULT false,
    is_muted BOOLEAN DEFAULT false,
    unread_count INTEGER DEFAULT 0,
    last_message_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(account_id, contact_id)
);

-- WhatsApp Messages Table
CREATE TABLE whatsapp_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES whatsapp_conversations(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    contact_id UUID NOT NULL REFERENCES whatsapp_contacts(id) ON DELETE CASCADE,
    whatsapp_message_id VARCHAR(255), -- WhatsApp's internal message ID
    content TEXT,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'document', 'audio', 'video', 'location', 'contact', 'sticker')),
    media_url TEXT, -- URL to stored media file
    media_filename VARCHAR(255),
    media_mime_type VARCHAR(100),
    media_size INTEGER,
    is_from_me BOOLEAN NOT NULL,
    status VARCHAR(20) DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed')),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    reply_to_message_id UUID REFERENCES whatsapp_messages(id),
    forwarded_from VARCHAR(255), -- Original sender if forwarded
    is_starred BOOLEAN DEFAULT false,
    metadata JSONB DEFAULT '{}', -- Additional message metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- WhatsApp Message Templates Table (for business messaging)
CREATE TABLE whatsapp_message_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(50) NOT NULL, -- marketing, utility, authentication
    language VARCHAR(10) DEFAULT 'en',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    template_data JSONB NOT NULL, -- WhatsApp template structure
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    UNIQUE(account_id, name)
);

-- WhatsApp Automation Rules Table
CREATE TABLE whatsapp_automation_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    trigger_type VARCHAR(50) NOT NULL, -- message_received, keyword, time_based, etc.
    trigger_conditions JSONB NOT NULL, -- Conditions for triggering the rule
    actions JSONB NOT NULL, -- Actions to perform when triggered
    priority INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- WhatsApp Broadcast Lists Table
CREATE TABLE whatsapp_broadcast_lists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    contact_ids UUID[] NOT NULL, -- Array of contact IDs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- WhatsApp Analytics Table
CREATE TABLE whatsapp_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    messages_sent INTEGER DEFAULT 0,
    messages_received INTEGER DEFAULT 0,
    messages_delivered INTEGER DEFAULT 0,
    messages_read INTEGER DEFAULT 0,
    new_conversations INTEGER DEFAULT 0,
    active_conversations INTEGER DEFAULT 0,
    response_time_avg INTERVAL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(account_id, date)
);

-- WhatsApp Webhook Events Table (for logging)
CREATE TABLE whatsapp_webhook_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB NOT NULL,
    processed BOOLEAN DEFAULT false,
    processed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_whatsapp_accounts_phone ON whatsapp_accounts(phone);
CREATE INDEX idx_whatsapp_accounts_status ON whatsapp_accounts(status);
CREATE INDEX idx_whatsapp_accounts_organization ON whatsapp_accounts(organization_id);

CREATE INDEX idx_whatsapp_contacts_account ON whatsapp_contacts(account_id);
CREATE INDEX idx_whatsapp_contacts_phone ON whatsapp_contacts(phone);
CREATE INDEX idx_whatsapp_contacts_account_phone ON whatsapp_contacts(account_id, phone);

CREATE INDEX idx_whatsapp_conversations_account ON whatsapp_conversations(account_id);
CREATE INDEX idx_whatsapp_conversations_contact ON whatsapp_conversations(contact_id);
CREATE INDEX idx_whatsapp_conversations_last_message ON whatsapp_conversations(last_message_at DESC);

CREATE INDEX idx_whatsapp_messages_conversation ON whatsapp_messages(conversation_id);
CREATE INDEX idx_whatsapp_messages_account ON whatsapp_messages(account_id);
CREATE INDEX idx_whatsapp_messages_timestamp ON whatsapp_messages(timestamp DESC);
CREATE INDEX idx_whatsapp_messages_whatsapp_id ON whatsapp_messages(whatsapp_message_id);
CREATE INDEX idx_whatsapp_messages_status ON whatsapp_messages(status);

CREATE INDEX idx_whatsapp_templates_account ON whatsapp_message_templates(account_id);
CREATE INDEX idx_whatsapp_templates_status ON whatsapp_message_templates(status);

CREATE INDEX idx_whatsapp_automation_account ON whatsapp_automation_rules(account_id);
CREATE INDEX idx_whatsapp_automation_active ON whatsapp_automation_rules(is_active);

CREATE INDEX idx_whatsapp_analytics_account_date ON whatsapp_analytics(account_id, date);
CREATE INDEX idx_whatsapp_webhook_events_processed ON whatsapp_webhook_events(processed);
CREATE INDEX idx_whatsapp_webhook_events_created ON whatsapp_webhook_events(created_at);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_whatsapp_accounts_updated_at BEFORE UPDATE ON whatsapp_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_contacts_updated_at BEFORE UPDATE ON whatsapp_contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_conversations_updated_at BEFORE UPDATE ON whatsapp_conversations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_messages_updated_at BEFORE UPDATE ON whatsapp_messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_templates_updated_at BEFORE UPDATE ON whatsapp_message_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_automation_updated_at BEFORE UPDATE ON whatsapp_automation_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_broadcast_updated_at BEFORE UPDATE ON whatsapp_broadcast_lists FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
